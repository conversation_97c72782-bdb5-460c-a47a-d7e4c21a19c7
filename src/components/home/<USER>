declare const window: any;

import React, {
  useEffect,
  useState,
} from "react";
import { Mo<PERSON>, Button, Popover } from "antd";
import Router from "next/router";
import { useRouter } from "next/router";
import * as constants from "@/constants/appConstants";
import user from "@/images/menu/user.png";
import logoutIcon from "@/images/menu/logout.png";

import {
  reqLoginToken,
  reqLoginUrl,
  reqLogout,
  reqLoginWaiWang,
  reqLogoutWaiWang,
} from "@/service/login";

import { reqUserInfo, reqProjectSelectList } from "@/service/common";
import { waiWangHosts } from '@/config/commonConfig';

import homeStyles from "@/styles/Home.module.scss";
import ailogo from "@/images/ailogo.png";
import logo from "@/images/menu/agent-logo.png";
import logocontent from "@/images/menu/logocontent.svg";
import goBtn from "@/images/menu/goBtn.svg";
import { isNullOrEmpty } from "@/utils/common";
import dynamic from "next/dynamic";
import { goToLoginWithClientUserInfo, goToLoginWithYunPanToken, goToLoginWithCompanyBrainToken, goClientUserInfoCommon } from "@/utils/tuituiLogin.js";

const MobileHomePageNoSSR = dynamic(() => import('@/components/home/<USER>'), {
  ssr: false
})


let isInitPage = false;
export default function HomePage() {
  const router = useRouter();
  const { pathname } = router
  const ticket = router.query.ticket;
  const [userName, setUserName] = useState("");
  const querySrc = router.query.src;
  const [isZhiNao, setIsZhiNao] = useState(false);
  const [isOuterNet, setIsOuterNet] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [isMobile, setIsMobile] = useState(false); // 是否是移动端
  const [isFromTuituiBoard, setIsFromTuituiBoard] = useState(false); // 是否来自推推看板
  const [isFromZhiYuBoard, setIsFromZhiYuBoard] = useState(false); // 是否来自织语看板
  const [isFromYunPanBoard, setIsFromYunPanBoard] = useState(false); // 是否来自云盘看板
  const [isFromCompanyBrainBoard, setIsFromCompanyBrainBoard] = useState(false); // 是否来自企业大脑看板
  const [isFromQpaasBoard, setIsFromQpaasBoard] = useState(false); // 是否来自QPaas平台
  const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false); // 是否来自llmops平台
  const [isVisibleLoginOut, setIsVisibleLoginOut] = useState(true);
  const [userInfo, setUserInfo] = useState({
    user_id: 0,
    username: "",
    email: "",
    phone_number: "",
    // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
    system_role: 0
  });
  const [isReloadLoginBySearch, setIsReloadLoginBySearch] = useState(true);

  // 判断location search是否改变
  const isChangeLocationSearch = () => {
    let searchVal = localStorage.getItem(constants.prompt_isReloadLoginBySearch) || '';
    console.log('home', location.search, searchVal, location.search !== searchVal)
    return isFromZhiYuBoard && searchVal !== location.search && isReloadLoginBySearch;
  }
  // 来自推推看板或是织语看板 做单点登录
  const goToSignalLoginFromBoard = async () => {
    if (!isFromTuituiBoard && !isFromZhiYuBoard && !isFromYunPanBoard && !isFromCompanyBrainBoard && !isFromQpaasBoard && !isFromLlmopsBoard) {
      return;
    }
    setIsReloadLoginBySearch(false);
    localStorage.setItem(constants.prompt_isReloadLoginBySearch, location.search);

    let res = null;
    if (isFromYunPanBoard) {
      res = await goToLoginWithYunPanToken();
    } else if (isFromCompanyBrainBoard) {
      res = await goToLoginWithCompanyBrainToken();
    } else if (isFromTuituiBoard || isFromZhiYuBoard) {
      res = await goToLoginWithClientUserInfo(isFromTuituiBoard ? 1 : 2);
    } else {
      let source_type = '';
      if (isFromQpaasBoard) {
        source_type = 'qpaas';
      }
      else if (isFromLlmopsBoard) {
        source_type = 'llmopsBoard';
      }
      source_type && (res = await goClientUserInfoCommon(source_type));
    }
    // const res = isFromYunPanBoard ? await goToLoginWithYunPanToken() :
    //   (isFromCompanyBrainBoard ? await goToLoginWithCompanyBrainToken() :
    //     await goToLoginWithClientUserInfo(isFromTuituiBoard ? 1 : 2));

    if (res && res.token) {
      localStorage.setItem(constants.prompt_authorization, "Bearer " + res.token);
      getUserInfo();
    } else {// 单点登录报错后，走正常登录逻辑
      console.log('单点登录报错----->')
      localStorage.setItem(constants.prompt_isTuituiBoard, '');
      localStorage.setItem(constants.prompt_isZhiYuBoard, '');
      localStorage.setItem(constants.prompt_isYunPanBoard, '');
      localStorage.setItem(constants.prompt_isCompanyBrainBoard, '');
      localStorage.setItem(constants.prompt_userName, '');
      localStorage.setItem(constants.prompt_authorization, '');
      localStorage.setItem(constants.prompt_teamId, '');
      localStorage.setItem(constants.prompt_targetUrl, '')
      localStorage.setItem(constants.prompt_isReloadLoginBySearch, '');
      localStorage.setItem(constants.prompt_isQpaasBoard, '')
      localStorage.setItem(constants.prompt_isLlmopsBoard, '')
      setIsFromTuituiBoard(false);
      setIsFromZhiYuBoard(false);
      setIsFromYunPanBoard(false);
      setIsFromCompanyBrainBoard(false);
      setIsFromQpaasBoard(false);
      setIsFromLlmopsBoard(false);
      Router.replace('/');
    }
  }

  const getToken = async () => {
    const res = await reqLoginToken({
      ticket,
    });
    if (res && res.token) {
    }

    // localStorage.setItem(constants.prompt_authorization, "Bearer " + res.token);
    getUserInfo();
  };
  /** 内外网登录用户获取用户信息 */
  const getUserInfo = async () => {
    console.log('HomePage.ts----------------------getUserInfo')
    const res = await reqUserInfo({});
    if (isNullOrEmpty(res)) {
      return;
    }
    // 处理亿方云盘登录的默认链接跳转问题
    if (isFromYunPanBoard || isFromCompanyBrainBoard) {
      console.log('window.location.href----------------------:', window.location.href)
      localStorage.setItem(constants.prompt_targetUrl, window.location.href);
    }
    localStorage.setItem(constants.prompt_userId, res.user_id);
    localStorage.setItem(constants.prompt_userName, res.username);
    const resUserName = res.username;
    setUserName(resUserName);
    setUserInfo({
      ...userInfo,
      user_id: res.user_id,
      username: resUserName,
      email: res.email,
      phone_number: res.phone_number,
      system_role: res.system_role
    });

    // 触发自定义事件通知其他组件
    const event = new CustomEvent(constants.prompt_systemRoleUpdated, {
      detail: { systemRole: res.system_role }
    });
    (typeof window !== 'undefined') && window.dispatchEvent(event);
    // const phone_number = res.phone_number;
    const isFirstLogin = localStorage.getItem(constants.prompt_isFirstLogin) === 'true' ? true : false; // 是否是可以自动登录逻辑
    if (!isFirstLogin) {
      localStorage.setItem(constants.prompt_isFirstLogin, 'false');
      goToAgents()
    }
  }

  const goToAgents = async() => {
    // 判断是否未登录之前的跳转
    const prompt_targetUrl = localStorage.getItem(constants.prompt_targetUrl);
    console.log("🚀 ~ goToAgents ~ prompt_targetUrl----------------------:", prompt_targetUrl)
    if (prompt_targetUrl && prompt_targetUrl.length > 0) {
      Router.replace(prompt_targetUrl);
      localStorage.setItem(constants.prompt_targetUrl, '');
      return;
    }
    const localTeamId = localStorage.getItem(constants.prompt_teamId);


    const res = await reqProjectSelectList({})
    let myProjectId = ''
    res?.data?.forEach((p) => {
      if(p.team_type == 4) {
        myProjectId = p.id
      }
    })
    
    if (localTeamId) {
      Router.replace(`/flowList?teamId=${localTeamId}`);// 内网用户登录成功，去发现页面
    } else {
      Router.replace(`/flowList?teamId=${myProjectId}`);// 内网用户登录成功，去发现页面
    }
  }

  useEffect(() => {
    if (ticket) {
      getToken();
      return;
    } else {
      if (isInitPage) {
        return;
      }
    }
    // 智能体sdk使用页面，不需要登录逻辑
    if (pathname.indexOf('agentSDK') > -1) {
      return
    }

    if (localStorage.getItem(constants.prompt_userName)) { // 已经登录的逻辑
      setUserName(localStorage.getItem(constants.prompt_userName) || "");
      // console.log('已经登录的逻辑')
    }
    getUserInfo();
    isInitPage = true;
  }, [ticket]);

  useEffect(() => {
    const prompt_userName = localStorage.getItem(constants.prompt_userName);
    if ((!(prompt_userName && prompt_userName.length > 0) && (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard)) || (isChangeLocationSearch())) { // 未登录的情况下，来自推推看板或是织语看板 做单点登录
      goToSignalLoginFromBoard();
    }
  }, [isFromTuituiBoard, isFromZhiYuBoard, isFromYunPanBoard, isFromCompanyBrainBoard, isFromQpaasBoard, isFromLlmopsBoard]);

  useEffect(() => {
    if (localStorage.getItem(constants.isZhiNao) === "yes") {
      setIsZhiNao(true);
    }

    if (localStorage.getItem(constants.prompt_isTuituiBoard) === "true" || window.location.href.indexOf("tuituiBoard") !== -1) {
      setIsFromTuituiBoard(true);
      localStorage.setItem(constants.prompt_isTuituiBoard, "true");
    }
    if (localStorage.getItem(constants.prompt_isZhiYuBoard) === "true" || window.location.href.indexOf("zhiyuBoard") !== -1) {
      setIsFromZhiYuBoard(true);
      localStorage.setItem(constants.prompt_isZhiYuBoard, "true");
    }
    if (localStorage.getItem(constants.prompt_isYunPanBoard) === "true" || window.location.href.indexOf("yunpanBoard") !== -1) {
      setIsFromYunPanBoard(true);
      localStorage.setItem(constants.prompt_isYunPanBoard, "true");

    }

    if (localStorage.getItem(constants.prompt_isCompanyBrainBoard) === "true" || (window.location.href.indexOf("companyBrain") !== -1)) {
      setIsFromCompanyBrainBoard(true);
      localStorage.setItem(constants.prompt_isCompanyBrainBoard, "true");
    }

    if (localStorage.getItem(constants.prompt_isQpaasBoard) === "true" || (window.location.href.indexOf("qpaasBoard") !== -1)) {
      setIsFromQpaasBoard(true);
      localStorage.setItem(constants.prompt_isQpaasBoard, "true");
    }

    if (localStorage.getItem(constants.prompt_isLlmopsBoard) === "true" || (window.location.href.indexOf("llmopsBoard") !== -1)) {
      setIsFromLlmopsBoard(true);
      localStorage.setItem(constants.prompt_isLlmopsBoard, "true");
    }

    if (getHrefIncludes([constants.prompt_isZhiKeBoard])) {
      setIsVisibleLoginOut(false);
    }


    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      setIsOuterNet(waiWangHosts.includes(hostname));
    }
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

    setIsMobile(isMobileDevice)
  }, []);

  useEffect(() => {
    if (querySrc === "zhinao") {
      localStorage.setItem(constants.isZhiNao, "yes");
      setIsZhiNao(true);
    }
  }, [querySrc]);

  const getHrefIncludes = (contentList: string[]) => {
    let flag = false;
    if (contentList.includes(localStorage.getItem(constants.prompt_platform) || '')) {
      return true;
    }
    contentList.forEach((item: any) => {
      if (window.location.href.includes('source=' + item)) {
        flag = true;
      }
    });
    return flag;
  }

  const login = async () => {
    console.log("🚀 ~ login ~ login----------------------:");
    const res = await reqLoginUrl({});
    Router.push(res.login_url);
  };
  const logout = async () => {
    const res = await reqLogout({});
    // Router.push(res.logout_url)
    // location.reload();
    clearUserInfoAndStatus();
  };

  const loginWaiWang = async (name: any) => {
    const res = await reqLoginWaiWang({});
    if (!res?.is_permitted) {
      // true 在白名单里 false 不在白名单

      modal.info({
        title: '提示',
        // content: <label>您好，请您发送账户名称和使用场景到 <br /> ******************申请试用，感谢关注。</label>,
        content: <label>您好，<a href='https://daily.fe.qihoo.net/superai-web-1024/index.html'>点击链接</a>，进入Agent智能体开发大赛页面申请试用，感谢您的关注！</label>
      });
      return false;
    }


    if (res?.token) {
      localStorage.setItem(constants.prompt_authorization, "Bearer " + res?.token);
      localStorage.setItem(constants.prompt_userName, name);
      // setIsLogin(true);
      /**外网登录用户 缺少 用户Id本地设置*/
      // localStorage.setItem(constants.prompt_userId, res?.user_id); ??
    }
    setUserName(name);
    getUserInfo();
  };

  const logoutWaiWang = async () => {
    const res = await reqLogoutWaiWang({});

    if (typeof window !== "undefined") {
      const jquery: any = window.$;
      jquery.getScript("//s.ssl.qhimg.com/quc/quc7.js").then(function () {
        const q: any = window.QHPass;
        q.signOut();
      });
    }

    clearUserInfoAndStatus();
  };

  const clearUserInfoAndStatus = () => {
    setUserName("");
    // setIsLogin(false);
    setUserInfo({
      user_id: 0,
      username: "",
      email: "",
      phone_number: "",
      // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
      system_role: 0
    })
    localStorage.setItem(constants.prompt_userId, "");
    localStorage.setItem(constants.prompt_userName, "");
    localStorage.setItem(constants.prompt_authorization, "");
    localStorage.removeItem(constants.prompt_enable_canvas)
  }

  const goToTryHandel = () => {
    if (localStorage.getItem(constants.prompt_userName)) { // 已经登录的逻辑
      setUserName(localStorage.getItem(constants.prompt_userName) || "");
      getUserInfo();
    } else {
      if (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard) {
        goToSignalLoginFromBoard()
        return;
      }
      if (isOuterNet) {
        if (typeof window !== "undefined") {
          const jquery: any = window.$;
          jquery
            .getScript("//s.ssl.qhimg.com/quc/quc7.js")
            .then(function () {
              const q: any = window.QHPass;
              q.init({ src: "pcw_agent", primaryColor: '#006BFF' });
              q.signIn(function () {
                q.getUserInfo(
                  function (u: any) {
                    console.log(u, 111);
                    setUserName(u.username);
                    loginWaiWang(u.username);
                  },
                );
              });
            });
        }
      } else {
        login()
      }
    }
    localStorage.setItem(constants.prompt_isFirstLogin, 'false');
  }
  console.log(isFromTuituiBoard, isFromZhiYuBoard, isFromYunPanBoard, isFromCompanyBrainBoard, isFromQpaasBoard, isFromLlmopsBoard, isOuterNet, 11)

  return (
    isMobile ? <MobileHomePageNoSSR goToTryHandel={goToTryHandel} />
      : (<div className={homeStyles.homeBg}>
        <div className={homeStyles.header}>
          <div  className={homeStyles.headerInner}>
            <a className={homeStyles.title} href="/">
              {isZhiNao ? (
                <img src={ailogo.src} height="35" />
              ) : (
                <img src={logo.src} height="31" style={{marginTop: 4}} />
              )}
            </a>
            <div className={homeStyles.userBox}>
              <a href="https://bot.n.cn/tools/aiagent" target="_blank">案例研究</a>
              <a href="https://easydoc.soft.360.cn/doc?project=e0dafabd6582cf6faa2290b953f105e0&doc=c90cb1593ea4a909dbb18f7fdd618a3d&config=title_menu_toc" target="_blank">文档中心</a>
              <div className={homeStyles.login}>
                {userName ? (
                  <div className={homeStyles.myAgent} >
                    <Popover
                      title=""
                      trigger="hover"
                      arrow={false}
                      placement={"bottom"}
                      content={
                        <div className={homeStyles.userInfoWrapper}>
                          <div className={homeStyles.userNameBg}>
                            <img
                              src={user.src}
                              width={24}
                              className={homeStyles.headSculpture}
                            />
                            <span className={"boldFont"}>{userInfo.username}</span>
                          </div>
                          {isVisibleLoginOut ? <div className={homeStyles.userInfoInner}>
                            <img src={logoutIcon.src} width="14" />
                            <span onClick={isOuterNet ? logoutWaiWang : logout}>
                              退出登录
                            </span>
                          </div> : null}
                        </div>
                      }>
                      <div className={homeStyles.userContent} onClick={goToTryHandel}>
                        <img src={user.src} alt="" />
                        <span>{userInfo.username}</span>
                      </div>
                    </Popover>
                  </div>
                ) : (
                  <span
                    id="signIn"
                    className={homeStyles.loginBtn}
                    onClick={
                      (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard) ? goToSignalLoginFromBoard : (
                        isOuterNet
                          ? () => {
                            if (typeof window !== "undefined") {
                              const jquery: any = window.$;
                              jquery
                                .getScript("//s.ssl.qhimg.com/quc/quc7.js")
                                .then(function () {
                                  const q: any = window.QHPass;
                                  q.init({ src: "pcw_agent", primaryColor: '#006BFF' });
                                  q.signIn(function () {
                                    q.getUserInfo(
                                      function (u: any) {
                                        console.log(u, 222)
                                        setUserName(u.username);
                                        loginWaiWang(u.username);
                                      },
                                    );
                                  });
                                });
                            }
                          }
                          : login
                      )
                    }
                  >
                    <span>开始探索</span>  
                  </span>
                )}
              </div>
            </div>
          </div>
          
        </div>
        <div className={homeStyles.homeBody}>
          <div className={homeStyles.homeBodyContent}>
            <div className={homeStyles.bodyTitle}>
              <img src={logocontent.src} width={500} />
            </div>
            <div className={homeStyles.bodySubTitle}>
              全球首个多智能体协同平台。全程自然语言编程极简搭建，全程解放人力自动执行。个人及组织皆可自主创建专属智能体。不同领域智能体可自由组队协作，助力个人成为超级个体，企业变身超级组织。已累计创建数以万计领域专家型智能体，支撑中国最大智能体社区运行。
            </div>
            <div>
              <img src={goBtn.src} className={homeStyles.bodyBtn} onClick={goToTryHandel} />
            </div>
            <div className={homeStyles.contentWrapper}>
              <img src="https://p2.ssl.qhimg.com/t110b9a93015b4b5bceae6700e7.png" className={homeStyles.contentWrapper} alt="" />
            </div>
          </div>
        </div>
        <div className={homeStyles.homeFooter}>
          <div className={homeStyles.footerCopyright}>
            <p>
              Copyright © 2005-<span>2024</span>版权所有
              天津三六零快看科技有限公司
              <a href="https://jubao.tjcac.gov.cn/" target="_blank" style={{ marginLeft: '8px', color: "#999" }}>天津举报中心</a>
            </p>
            <p>
              <a href="https://beian.miit.gov.cn/" target="_blank" style={{ color: '#999' }}>津ICP备20006251号-2</a>
              <span style={{ marginLeft: '8px' }}>违法和不良信息举报电话：022-88355238 022-88355239</span>
              <a href="//www.beian.gov.cn/portal/registerSystemInfo?recordcode=12011602001446" target="_blank" style={{ color: "#999" }}>
                <img src="//p3.ssl.qhimg.com/t01fc7b7bb8e1952d11.png" alt="备案" style={{ verticalAlign: 'middle', margin: '-1px 4px 0' }} />
                津公网安备 12011602001446号
              </a>
            </p>
          </div>
          {contextHolder}
        </div>
      </div>)
  );
}
