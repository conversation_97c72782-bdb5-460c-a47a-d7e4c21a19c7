declare const window: any;

import { use<PERSON>allback, useEffect, useState, useRef, useMemo } from 'react';
import { useRecoilCallback, useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { Button, Spin, message, Space, Tooltip, notification } from 'antd';

import { clone, init } from 'ramda';
import { debounce, set } from 'lodash'
import md5 from 'blueimp-md5'
import { Resizable } from 're-resizable'
import moment from 'moment'

import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import styles from '@/styles/flow3.0/Common.module.scss'

import { waiWangHosts } from '@/config/commonConfig';
import { FlowEditor } from '../flowEditor3.0/Editor'

import { cloneBlockAtom, isRunTestStatusAtom, edgesAtom, blocksAtom, addAgentDataAtom, isEditActiveAtom, deleteInfoAtom, isDisabled<PERSON>tom, runNodeResult<PERSON>tom, loopConfigs<PERSON><PERSON>, activeBlockId<PERSON>tom, createEdgeInfo<PERSON>tom } from '@/atoms/flowEditorAtoms';

import {
    reqFlowDetail, reqBlockDetail, reqBlocksDetail, reqDebugFlow, reqRunFlow, reqStopFlow, reqStopTeamFlow,
    reqAddBlock, reqLoopCreateBlock, reqLoopDeleteBlock, reqValidateFlow,
    reqUpdateBlock, reqDeleteBlock, reqQuoteKnowledge, reqValidateBlock,
    reqFlowVersionDetail, reqStopVersionFlow, reqVersionBlocksStatus, reqCopyVersionFlow,
    reqTestTeamPublishFlow, reqTestPublicPublishFlow, reqTeamFlowBlocksDetail, getSSEFlowData,
    reqBlocksDetailContinueRun,
    reqSaveFlowPosition, reqSavePromptHistory, reqSaveFlow
} from '@/service/flow3.0';

import Router, { useRouter } from 'next/router'

import MarkDownModal from './MarkDownModal'
import BlockTitle from './block/commons/BlockTitle'

import BlockAddMCP from "./block/BlockAddMCP";
import BlockAddFlow from "./block/BlockAddFlow";
import BlockAddNmAgent from "./block/BlockAddNmAgent";
import BlockAddNmMcp from "./block/BlockAddNmMcp";
import BlockAddFlowGui from '@/components/flow3.0/block/BlockAddFlowGui';

import CreateCommonModal from "@/components/common/CreateModal";
import BlockAgentCondition from './block/BlockAgentCondition';
import BlockIterationAgent from './block/BlockIterationAgent';
import TutorialGuide from '@/components/common/TutorialGuide';

import BlockAgent from './block/BlockAgent';

import Editor from '@monaco-editor/react';
import loader from '@monaco-editor/loader';

loader.config({ paths: { vs: './vs' } });

import { initPromptList } from '@/config/flowConfig'
import { addNewBlock, getType, getResult, initJsonEditor, blockNameObj, checkAutoInsertMention, checkAutoInsertUserInput } from './flowFunc';

import editBtn from '@/images/editBtn.png';
import leftBtn from '@/images/common/leftBtn.svg';
import image from '@/images/common/image.svg';
import debugSvg from '@/images/newFlow/debug.svg';
import closeSvg from '@/images/newFlow/close.svg';

import errorTip from '@/images/errorTip.svg';
import { LoadingOutlined } from '@ant-design/icons';

import { blockMCPType, blockNamiType } from '@/config/newflow/flowConfig';
import flowExportJsonFile from '@/utils/FlowConstants/flowExportJsonFileV3';
// import FlowDetailImportFile from '@/components/commonComponents/flowDetailV3ImportFile/flowDetailV3ImportFile';
import Show from '@/components/commonComponents/show/Show';
import RunTestPage from '@/components/flow3.0/block/commons/RunTest';
import RunFlowPage from '@/components/flow3.0/block/commons/Run/RunFlow';

import { prompt_authorization } from '@/constants/appConstants';
import ImgRunDebugPlay from '@/images/flow3.0/runPlayBlue.svg';
import { uploadSnapShot } from '@/utils/flow';

interface iConfigObj {
    [key: number]: object;
}

// 发布前 整体flow 【运行】
let flowPollTimer: any;
// 发布前 单个block 【调试】
let pollTimer: any;
// 发布后 整体flow 【运行】分团队内、公开
let publishFlowPollTimer: any;

let updateTime = 0;
interface ApiConfirmParamsOrigin {
    id: any,
    title: any
    description: any
    okText: string,
    cancelText: string
    showCancel: boolean,
    is_updated: number,
    type: string,
    versionInfo: any
}

const apiConfirmParamsOrigin: ApiConfirmParamsOrigin = {
    id: '',
    title: '',
    description: '',
    okText: '',
    cancelText: '',
    showCancel: true,
    is_updated: 0,
    type: '',
    versionInfo: {}
}

const isSse = false;
// 边存在edgesatom 
// loopconfigsatom 存迭代的边以及点的数组 
// 所有的点存在blocks blocksatom只用来取值，不能更新

export default function Flow(props: any) {

    const { backFuc, oFlowId, oTeamId, oFlowStatus, addFlow, setFlowVersion } = props

    const router = useRouter();
    const [openImportModal, setOpenImportModal] = useState(false); //导入文件弹框

    // 不可编辑 分为自己项目的和发现里的（没有teamid）
    // flowStatus 
    // uneditable: 不可编辑（对应【运行 、复制】 按钮） 
    // 不可编辑 分为自己项目和发现里的（没有teamid）
    // editable: 可编辑 （对应【保存 运行 发布】按钮）
    // addable: 可添加 （对应 【运行、添加】按钮）
    const [flowStatus, setFlowStatus] = useState(oFlowStatus)
    const [chainId, setChainId] = useState('')
    const [version, setVersion] = useState('')
    const [teamId, setTeamId] = useState('')
    const [flowType, setFlowType] = useState('')
    const [showFormInteractive, setShowFormInteractive] = useState(false); // 调试运行是否显示交互节点输入表单
	const [ resizableWidth, setResizableWidth ] = useState(440)

    const [ createEdgeInfo, setCreateEdgeInfo ] = useRecoilState(createEdgeInfoAtom)
    const [firstInitMATValue, setFirstInitMATValue] = useState(false);
    const [isMoreAgentTeams, setIsMoreAgentTeams] = useState(false);
    // 入口  1: 审核列表
    const from = router.query.from;

    useEffect(() => {
        const r: any = router.query.version
        setVersion(r)
    }, [router.query.version])

    useEffect(() => {
        setChainId(oFlowId || router.query.id)
    }, [oFlowId || router.query.id])

    useEffect(() => {
        setTeamId(oTeamId || router.query.teamId)
    }, [oTeamId || router.query.teamId])

    useEffect(() => {
        // flow_type
        setFlowType(router.query.flow_type + '' || '1')
    }, [router.query.flow_type])

    useEffect(() => {
        if (router?.query?.firstInit == 'true' && router.pathname === '/flowDetailV4') {
            setFirstInitMATValue(true);
        }
        if (router?.pathname === '/flowDetailV4') {
            setIsMoreAgentTeams(true);
        }
    }, [router])

    const [openMarkDown, setOpenMarkDown] = useState(false)
    const [openApi, setOpenApi] = useState(false)
    const [openAddFlow, setOpenAddFlow] = useState({
        open: false,
        isAddBlock: true
    })
    const [openAddGui, setOpenAddGui] = useState({
        open: false,
        isAddBlock: true
    })
    const [contentValue, setContentValue] = useState('')

    const [isWaiWang, setIsWaiWang] = useState(false);
    const [initLoading, setInitLoading] = useState(false);
    const [isInitData, setIsInitData] = useState(false);
    const [blockLoading, setBlockLoading] = useState(false);
    const [flowLoading, setFlowLoading] = useState(false);
    const [stopLoading, setStopLoading] = useState(false);
    const [validateLoading, setValidateLoading] = useState(false);
    const [updateLoading, setUpdateLoading] = useState(false)
    const [isShowSet, setIsShowSet] = useState(true);
    const [isShowLlmParam, setIsShowLlmParam] = useState(false);
    const [promptTitle, setPromptTitle] = useState('Flow')
    const [promptDesc, setPromptDesc] = useState('')
    const [promptImage, setPromptImage] = useState('')
    // 所有节点
    const [blockIds, setBlockIds] = useState(new Array())

    // 发布状态，1:未发布，2:已发布
    const [publishStatus, setPublishStatus] = useState(1)
    const [blocks, setBlocks] = useState<any>(new Object())
    const [statusBlocks, setStatusBlocks] = useState<any>(new Object())
    // 节点位置
    const [blocksPosition, setBlocksPosition] = useState<any>(new Object())

    const blocksPositionRef = useRef<any>(null);
    const [loopConfigs, setLoopConfigsAtom] = useRecoilState(loopConfigsAtom)

    const [isEditActive, setIsEditActive] = useRecoilState(isEditActiveAtom)
    const setDeleteInfoAtom = useSetRecoilState(deleteInfoAtom)
    const setIsDisabledAtom = useSetRecoilState(isDisabledAtom)
    const [ isUserFullScreen, setIsUserFullScreen ] = useState(false)
    const [ isUploading, setIsUploading ] = useState(false)
    const runFlowDebugCancelFun = useRef(null);

    //判断是否在纳米客户端里面
    const isInNamiClient = useMemo(() => {
			return  ['nami', 'nami-business'].includes((router.query.src || '') as string) 
    }, [ router.query.src ])

    //退出当前页面
    const handleFlowBack = async () => {
        if(isUploading) {
            return;
        }
        setIsUploading(true)
		// 截图上传 保存flow
        await Promise.all([
            uploadSnapShot(document.querySelector('#canvas'), chainId),
            saveFlow()
        ])
        setIsUploading(false)

        if(window.self !== window.top) {
            top.postMessage({
                    action : "flowBack",
                    data : {
                            flow_id : chainId,
                            team_id : teamId,
                    }
            }, "*");
        } else {
            window.history.back()
        }
    }
    // 节点运行结果
    const [runNodeResultValue, setRunNodeResultValue] = useRecoilState(runNodeResultAtom);
    // 当前调试或运行的call_back_id
    const [currentRunCallBackId, setCurrentRunCallBackId] = useState('');

    useEffect(() => {
        setIsDisabledAtom(blockLoading || flowLoading)
    }, [blockLoading, flowLoading])

    // 当前flow信息
    const flowInfo = useMemo(() => {
        return {
            title: promptTitle,
            desc: promptDesc,
            image: promptImage,
            id: chainId,
            teamId: teamId
        }
    }, [promptTitle, promptDesc, promptImage, chainId, teamId])  
    
    const [firstBlockId, setFirstBlockId] = useState('')


    // 运行后刷新页面 当前loading的节点 
    const [currentLoadingId, setCurrentLoadingId] = useState('')
    // 调试后刷新页面 当前loading的节点
    const [currentLoadingDebugId, setCurrentLoadingDebugId] = useState('')
    const [activeBlockId, setActiveBlockId] = useState('')
    const [activeFormInteractionBlockId, setActiveFormInteractionBlockId] = useState(''); // 当前交互节点id
    const [activeErrorId, setActiveErrorId] = useState('')

    const [activeLlm, setActiveLlm] = useState('')
    const [activeRunningId, setActiveRunningId] = useState(0)
    const [logId, setLogId] = useState('')
    const [callbackId, setCallbackId] = useState('')

    const [saveTime, setSaveTime] = useState("");

    const [promptList, setPromptList] = useState(initPromptList)
    const [modelList, setModelList] = useState(new Array())

    const [cloneBlock, setCloneBlockAtom] = useRecoilState(cloneBlockAtom)

    const [cloneBlockItem, setCloneBlockItem] = useState('')

    const [isEditNameModal, setIsEditNameModal] = useState(false);

    const flowRef = useRef(null)
    const [clearStamp, setClearStamp] = useState(0)

    useEffect(() => {
        blocksPositionRef.current = blocksPosition;
    }, [blocksPosition]);

    // 边
    const [atomEdges, setAtomEdges] = useRecoilState(edgesAtom)
    const atomBlocks = useRecoilValue(blocksAtom)

    // 试运行
    const [isRunTest, setIsRunTest] = useState(false);
    const [runTestModel, setRunTestModel] = useState('single'); // 单个调试 single 整体运行all
    const [isRunTestStatus, setIsRunTestStatus] = useRecoilState(isRunTestStatusAtom);
    const isRunTestRef = useRef(isRunTestStatus);
    useEffect(() => {
        isRunTestRef.current = isRunTestStatus;
    }, [isRunTestStatus]);

    useEffect(() => {
        // 关闭试运行窗口
        if (!flowLoading && !blockLoading) {
            setIsRunTest(false);
        }
    }, [activeBlockId]);

    useEffect(() => {
        if (flowLoading || blockLoading) {
            setIsRunTestStatus(true);
        } else {
            setIsRunTestStatus(false);
        }
    }, [flowLoading, blockLoading])


    useEffect(() => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname;
            setIsWaiWang(waiWangHosts.includes(hostname))
        }

        return () => {
            clearInterval(flowPollTimer);
            clearInterval(pollTimer);
            clearInterval(publishFlowPollTimer);
        };
    }, [])
    // 注册document点击事件，点击非弹窗地方，关闭弹窗
    useEffect(() => {
        const closeModal = (event: any) => {
            const tDom = event.target;
            const tClass = tDom.getAttribute('class');
            console.log(tClass,event,'---tClass')

            if (!isRunTestRef.current && tClass?.includes('gedit-playground-layer')) {
                setIsShowSet(false);
                setIsRunTest(false);
                setActiveBlockId('');
                setIsEditActive(false)
            }
            if(tClass?.includes('gedit-playground-layer')) {
                // 两者一样表示是点击事件
                 if(event.layerX == event.clientX){
                    setClearStamp(new Date().getTime())
                }
            }
        };
        document.getElementById("reactFlowWrapper")?.addEventListener('click', closeModal, false);
        return () => {
            document.getElementById("reactFlowWrapper")?.addEventListener('click', closeModal, false);
        }
    }, [])

    useEffect(() => {
        if (chainId) {
            getFlowDetail()
            getFlowPublishDetail()
        }
    }, [chainId + version])

    // 保存prompt 历史记录
    const handleSavePromptHistory = async () => {
        if (chainId && activeBlockId && blocks[activeBlockId] && blocks[activeBlockId]?.prompts) {
            try {
                let res = await reqSavePromptHistory(
                    {
                        "template_id": chainId,
                        "block_index": activeBlockId,
                        "prompt": blocks[activeBlockId]?.prompts?.user || '',
                        "user_params": blocks[activeBlockId]?.prompts?.user_params || []
                    }
                );
            } catch (error) {
                
            }
        }
    }

		// 监听createEdgeInfo变化， 提示词自动引用节点输出
		useEffect(() => {
			if(createEdgeInfo?.sourceNodeID && createEdgeInfo?.targetNodeID) {
				const { sourceNodeID, targetNodeID } = createEdgeInfo
				if(blocks[targetNodeID]) {
					checkAutoInsertMention(blocks, sourceNodeID, targetNodeID, setBlocks)
					setCreateEdgeInfo({
						sourceNodeID: '',
						targetNodeID: ''
					})
				}
			}
		}, [ createEdgeInfo, blocks ])

    // 交互节点输入内容点击确定,继续运行事件
    const formInteractiveConfirm = () => {
        const type = runTestModel == 'all' ? 'flow' : 'debug';
        setBlockLoading(true);
        handleContinueRun(type, blocks[activeFormInteractionBlockId].result);
    }

    // 判断是否在循环节点内
    const isInLoop = (blockid: any, blocks: any): boolean => {
        if (blocks[blockid]?.type == 'nami_agent_loop' || blocks[blockid]?.parentId || blocks?.[blockid]?.loop_id) return true
        return false
    }

    const getBlockName = (blocks: any, type: any) => {
        const nameNumberArr = []
        Object.keys(blocks).forEach((b: any) => {
            if (blocks[b]?.type == type) {
                const number = blocks[b]?.name?.match(/\d+/g);
                if (number?.length) {
                    nameNumberArr.push(+number[number.length - 1])
                }
            }
        })
        // let minNumber = 0;
        // if(nameNumberArr.length) {
        //     minNumber = Math.min(...nameNumberArr)
        //     while (true) {
        //         if (!nameNumberArr.includes(minNumber + 1)) {
        //             return minNumber;
        //         }
        //         // 找到下一个更小的候选值（排除当前 min）
        //         const nextCandidates = [...nameNumberArr].filter(num => num > minNumber);
        //         if (nextCandidates.length === 0) break;

        //         minNumber = Math.min(...nextCandidates);
        //     }
        // }


        let minNumber = 0;
        if (nameNumberArr.length) {
            minNumber = Math.max(...nameNumberArr)
        }
        return blockNameObj[type] + '_' + (minNumber + 1)
    }

    const updateAgentId = useCallback((block_id, agent_id) => {
        // 更新agent_id
        setBlocks(p => {
            const pre:any = clone(p)
            console.log(pre,block_id,22334455)
            pre[block_id].agent_id = agent_id
            return {...pre}
        })
    },[blocks])

    const addEditorBlock = useRecoilCallback(({ snapshot }) => async (params: any, p_blocks: any, cloneBlockItem: any) => {

        // 来自广场的agent数据
        const curAddAgentData = await snapshot.getPromise(addAgentDataAtom);
        const blocks = await snapshot.getPromise(blocksAtom);

        const { type, p_preBlockId, p_targetBlockId, p_sourcePortId = '', position, isDragEdgeAdd } = params
        console.log(type, '--', p_preBlockId, p_targetBlockId, isDragEdgeAdd, '------1122334455')
        // 是否是粘贴
        const isClone = JSON.stringify(cloneBlockItem) != '{}';
        const isSourceLoop = blocks?.[p_preBlockId]?.type == 'nami_agent_loop'
        const isinloop = isInLoop(p_preBlockId, blocks)

        // 是否是循环节点后的+号，添加
        // 是否是循环节点内的第一个添加按钮 还是循环节点后的第一个添加按钮
        const isFirstAfterLoopBlock = blocks[p_preBlockId]?.type == 'nami_agent_loop' && (p_targetBlockId || isDragEdgeAdd)
        // 错误处理
        if (isinloop && type == 'nami_agent_loop' && !isFirstAfterLoopBlock) {
          message.error('不支持嵌套迭代');
          return;
        }
       
        if (flowLoading || updateLoading) {
            return;
        }
        let newBlock: any;
        if(type == 'nami_agent_market') {
            newBlock = curAddAgentData
        }else {
            newBlock = addNewBlock(type, getBlockName(blocks, type) + '');
        }

        let newBlockFe = (isClone ? cloneBlockItem : newBlock)

        if(isDragEdgeAdd && blocks?.[p_preBlockId]?.loop_id) {
            // 从边上拖拽出来的面板添加的迭代里面的节点 需要减去迭代节点的位置
            const prePosition = blocksPositionRef.current[blocks?.[p_preBlockId]?.loop_id]
            position.x =  position.x - prePosition?.x
            position.y =  position.y - prePosition?.y
        }

        // 更新位置
        if (position?.x) {
            const p = {
                ...blocksPositionRef.current,
                [newBlockFe.id]: {
                    x: position?.x,
                    y: position?.y
                }
            }
            await reqSaveFlowPosition({
                template_id: chainId,
                meta: JSON.stringify(p)
            })
            setBlocksPosition(p)
        }
        
        if (isinloop && type != 'nami_agent_loop' && !isFirstAfterLoopBlock) { // 当前在循环节点内操作
            newBlockFe = {
                ...newBlockFe,
                loop_id: blocks[p_preBlockId]?.loop_id || blocks[p_preBlockId]?.id
            }
            getFEFlowDetail(newBlockFe, {
                sourceNodeID: isSourceLoop ? '' : p_preBlockId,
                sourcePortID: p_sourcePortId,
                targetNodeID: p_targetBlockId
            })
        } else {
            // 普通节点
            getFEFlowDetail(newBlockFe, {
                sourceNodeID: p_preBlockId,
                sourcePortID: p_sourcePortId,
                targetNodeID: p_targetBlockId
            })
        }
        if(isDragEdgeAdd || (p_preBlockId && newBlockFe.id)) {
            setCreateEdgeInfo({
                sourceNodeID: p_preBlockId,
                targetNodeID: newBlockFe.id
            })
        }
        
        setCloneBlockItem('');
        setCloneBlockAtom('')
        return newBlock
    }, [chainId, blocksPosition, updateLoading, flowLoading])

    const addEdges = (p:any, preEdges:any, newBlockFe:any) => {
        console.log(p,preEdges,newBlockFe,'===ppppaddEdgespppp===')
        const {isSourceCondition, isTargetCondition, isAddCondition, sourceNodeID, id, sourcePortID, targetNodeID } = p
        preEdges = preEdges.filter(item => !(item.sourceNodeID == sourceNodeID && (item.sourcePortID == sourcePortID || !sourcePortID) && item.targetNodeID == targetNodeID))
        console.log(preEdges,isSourceCondition,isTargetCondition,isAddCondition,'---preEdges1---')
        if(!isSourceCondition && !isTargetCondition){
            // 前后都是普通节点
            if(isAddCondition) {
                // 创建的是条件
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: '',
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: newBlockFe.branches[0].branch_id,
                        targetNodeID: targetNodeID
                    }
                ])
            }else {
                // 创建的是普通
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: '',
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: '',
                        targetNodeID: targetNodeID
                    }
                ])
            }
        }else if(isSourceCondition && !isTargetCondition){
            // 前条件 后普通
            if(isAddCondition) {
                // 创建的是条件
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: sourcePortID,
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: newBlockFe.branches[0].branch_id,
                        targetNodeID: targetNodeID
                    }
                ])
            }else {
                // 创建的是普通
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: sourcePortID,
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: '',
                        targetNodeID: targetNodeID
                    }
                ])

            }
        }else if(!isSourceCondition && isTargetCondition){
            // 前普通 后条件
            if(isAddCondition) {
                // 创建的是条件
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: '',
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: newBlockFe.branches[0].branch_id,
                        targetNodeID: targetNodeID
                    }
                ])
            }else {
                // 创建的是普通
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: '',
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: '',
                        targetNodeID: targetNodeID
                    }
                ])
            }
        }else if(isSourceCondition && isTargetCondition){
            // 前条件 后条件
            if(isAddCondition) {
                // 创建的是条件
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: sourcePortID,
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: newBlockFe.branches[0].branch_id,
                        targetNodeID: targetNodeID
                    }
                ])
            }else {
                // 创建的是普通
                // 添加新边
                preEdges = preEdges.concat([
                    {
                        sourceNodeID: sourceNodeID,
                        sourcePortID: sourcePortID,
                        targetNodeID: id
                    },
                    {
                        sourceNodeID: id,
                        sourcePortID: '',
                        targetNodeID: targetNodeID
                    }
                ])
            }
        }
        // 通过拖拽线增加节点 没有targetNodeID 所以需要过滤
        preEdges = preEdges.filter(item => item.targetNodeID && item.sourceNodeID)
        console.log(preEdges,'---preEdges2---')
        return preEdges;
    }

    const getFEFlowDetail = useRecoilCallback(({ snapshot }) => async (newBlockFe: any, edgeInfo:any ) => {
        // 新建节点 前端重新组装数据
        const e = await snapshot.getPromise(edgesAtom);
        const l = await snapshot.getPromise(loopConfigsAtom)
        const blocks = await snapshot.getPromise(blocksAtom)
        let preLoopConfigs = clone(l)
        let preEdges = clone(e)
        const {sourceNodeID, sourcePortID, targetNodeID}  = edgeInfo
        console.log(preLoopConfigs,blocks,'xxxreloadinfoxxx')
        // 前端组装数据
        const id = newBlockFe.id
        const type = newBlockFe.type
        const loopId = newBlockFe.loop_id
        setBlocks(pre => { 
            return {...pre, [id]: newBlockFe}
        })
        setBlockIds(pre => {
            return [...pre, id]
        })
        if(type == "nami_agent_loop") {
            // 更新loop_configs
            setLoopConfigsAtom({...preLoopConfigs, [id]: {
                blocks: {},
                edges: [],
                all_block_keys: []
            }});
        }
        if(loopId) {
            // 更新loop_configs
            preLoopConfigs[loopId] = { 
                ...preLoopConfigs[loopId],
                blocks: {},
                all_block_keys: [...preLoopConfigs[loopId].all_block_keys, id]
            }
            setLoopConfigsAtom({...preLoopConfigs});
        }
        if(sourceNodeID) {
            setDeleteInfoAtom({
                node: [],
                edge: [
                    {
                        sourceNodeID,
                        sourcePortID,
                        targetNodeID
                    }
                ]
            })

            // 测试场景
            // 前置节点是普通节点、条件节点，在边上加普通节点、条件节点，后置节点是普通节点、条件节点，8种场景
            const isSourceCondition = blocks[sourceNodeID]?.type == 'nami_agent_condition';
            const isTargetCondition = blocks[targetNodeID]?.type == 'nami_agent_condition';
            const isAddCondition = type == 'nami_agent_condition';
            if(loopId) {
                let innerEdges = preLoopConfigs[loopId].edges;
                const res = addEdges({isSourceCondition, isTargetCondition, isAddCondition, sourceNodeID, id, sourcePortID, targetNodeID}, innerEdges, newBlockFe)
                setLoopConfigsAtom({
                    ...preLoopConfigs,
                    [loopId]: {
                        ...preLoopConfigs[loopId],
                        blocks: {},
                        edges: res
                    }
                });
            }else {
                const res = addEdges({isSourceCondition, isTargetCondition, isAddCondition, sourceNodeID, id, sourcePortID, targetNodeID}, preEdges, newBlockFe)
                console.log(res, '---addEdges--')
                setAtomEdges(res)
            }
        }else{
            setDeleteInfoAtom({
                node: [],
                edge: []
            })
        }
        setActiveBlockId(newBlockFe.id)
        // if (isMoreAgentTeams) {
            message.success('添加成功');
        // }
    }, [isMoreAgentTeams])
    const getFEDeleteFlowDetail = useRecoilCallback(({ snapshot }) => async (blockId: any) => {
        const isDisabled = await snapshot.getPromise(isDisabledAtom)
        if(isDisabled) {
            message.info('运行中，请稍后再试。')
            return;
        }
        if(!blockId) {
            return;
        }
        // 删除节点 前端重新组装数据
        const e = await snapshot.getPromise(edgesAtom);
        const blocks = await snapshot.getPromise(blocksAtom);
        const l = await snapshot.getPromise(loopConfigsAtom)
        let preLoopConfigs = clone(l)
        let preEdges = clone(e)

        const loop_id = blocks[blockId]?.loop_id;
        let deleteId = [blockId]
        let deleteEdge = []
        setBlocks(pre => { 
            delete pre[blockId]
            return {...pre}
        })
        setBlockIds(pre => {
            return pre.filter(id => id !== blockId)
        })
        // 被删除的是循环节点
        if(blocks[blockId].type == 'nami_agent_loop'){
            console.log(JSON.stringify(preLoopConfigs),'----reloadinfoloopConfigs----')
            // 删除循环体内节点
            const allBlockKeys = preLoopConfigs[blockId].all_block_keys;
            deleteId = deleteId.concat(allBlockKeys)
            setBlocks(pre => { 
                allBlockKeys.forEach(id => {
                    delete pre[id]
                })
                return {...pre}
            })

            // 删除循环节点 以及边
            delete preLoopConfigs[blockId]
            setLoopConfigsAtom({...preLoopConfigs});
        }

        // 被删除的是循环体内节点
        if(loop_id) {
            preLoopConfigs[loop_id].all_block_keys = preLoopConfigs[loop_id].all_block_keys.filter(item => item != blockId)
            deleteEdge = deleteEdge.concat(preLoopConfigs[loop_id].edges.filter(item => item.sourceNodeID == blockId || item.targetNodeID == blockId))
            preLoopConfigs[loop_id].edges = preLoopConfigs[loop_id].edges.filter(item => !(item.sourceNodeID == blockId || item.targetNodeID == blockId))
            preLoopConfigs[loop_id].blocks = {}
                
            setLoopConfigsAtom({...preLoopConfigs});
        }else {
            deleteEdge = deleteEdge.concat(preEdges.filter(item => item.sourceNodeID == blockId || item.targetNodeID == blockId))
            preEdges = preEdges.filter(item => !(item.sourceNodeID == blockId || item.targetNodeID == blockId))
            setAtomEdges(preEdges)
        }
        
        setDeleteInfoAtom({
            node: deleteId,
            edge: deleteEdge
        })
        setActiveBlockId('')

    }, [])

    // 删除节点 
    const deleteBlock = useRecoilCallback(({ snapshot }) => async (id: any) => {
        const cur = await snapshot.getPromise(activeBlockIdAtom)
        console.log('deleteBlock', cur, id)
        getFEDeleteFlowDetail(cur)
    },[])

    // 获取发布详情
    const getFlowPublishDetail = async () => {
        if (from === 'templateAgent' && flowStatus === 'uneditable') {
            setPublishStatus(2)
        } else {
            // const res  = await reqFlowPublishDetail({
            //     template_id: chainId,
            //     team_id: teamId
            // })
            // res && setPublishStatus(res.publish_status || 1)
        }

    }
    
    // 获取flow详情
    const getFlowDetail = async (isLastResult = false, isRun = false, activeBlockId = '') => {
        // 用到的数据 
        // 所有的节点信息 blocks 
        setInitLoading(true)
        if (chainId) {
            let res: any;
            if (isRun) {
                res = await reqTeamFlowBlocksDetail({
                    template_id: chainId,
                    log_id: logId,
                    team_id: teamId
                })
            } else {
                if (version) {
                    res = await reqFlowVersionDetail({
                        template_id: chainId,
                        team_id: teamId,
                        version
                    })
                } else {
                    res = await reqFlowDetail({
                        template_id: chainId,
                        team_id: teamId
                    })
                }
            }

            if (!res) {
                setInitLoading(false)
                return;
            }
            
            const blocks: any = res?.flow_config?.blocks
            const blocksIds = Object.keys(blocks)
            setBlockIds(blocksIds)
            setBlocks(res?.flow_config?.blocks)
            setIsInitData(true)
            try {
                if (JSON.stringify(res?.flow_config?.meta) == '{}') {
                    const oldPosition = JSON.parse(
                        localStorage.getItem(chainId + teamId) || "{}"
                    );
                    setBlocksPosition(oldPosition)
                } else {
                    setBlocksPosition(JSON.parse(res?.flow_config?.meta))
                }
            } catch (e) {

            }

            // 前后端数据转换
            const edges = res?.flow_config?.edges || []
            setAtomEdges(edges)

            if (activeBlockId != '-1') {
                // -1是删除 不需要选中任何节点
                const curBlockId = activeBlockId
                setActiveBlockId(curBlockId)
                if(blocksIds.length == 1) {
                    setActiveBlockId(blocksIds[0])
                }
            } else {
                setActiveBlockId('')
            }
            if (res?.title) {
                setPromptTitle(res?.title);
                setPromptDesc(res?.desc || '');
                setPromptImage(res?.images)
                let isFindCurrentLoading = false;
                let debugRunId: any = '';
                let flowRunId: any = '';
                blocksIds?.forEach((key: any) => {
                    const obj: any = blocks[key];
                    if (obj) {
                        const result = obj.result;
                        if ((obj.status == '2' || obj.status == '7') && !isFindCurrentLoading && res.chain_status == '2') {
                            isFindCurrentLoading = true;
                            // 没运行完成就刷新页面
                            setCurrentLoadingId(key)
                            flowRunId = key;
                        }

                        if ((obj.status == '2' || obj.status == '7') && res.chain_status != 2 && obj.type !== 'input') {
                            // 调试没出结果刷新页面, 处理循环节点
                            let curKey = key;
                            if (obj.loop_id) {
                                curKey = obj.loop_id;
                            }
                            setCurrentLoadingDebugId(curKey)
                            debugRunId = curKey;
                        }
                        blocks[key] = {
                            id: key,
                            ...obj,
                            // status: (debugRunId || flowRunId) ? 0 : obj.status
                        }
                    }
                })
                setBlocks({ ...blocks })
            }

            // 更新loop_configs
            if (res?.flow_config?.loop_configs) {
                setLoopConfigsAtom(res.flow_config.loop_configs);
            }

            setInitLoading(false)
        }
    }

    const processFinishFlow = () => {
        setStopLoading(false);
        setBlockLoading(false);
        setFlowLoading(false);
        // 运行完成 没有走到的分支手动置状态
        blockIds.forEach((item, index) => {
            if (blocks[item].status == '2') {
                blocks[item].status = 8
            }
        })
    }

    const processBlockData = (item: any) => {
        // todo 
        // if (item?.status == '3' || item?.status == '4' || item?.status == '6') {
        //     const id = item.block_key
        //     blocks[id].result = item.result;
        //     blocks[id].status = item.status
        //     setBlocks({ ...blocks })
        //     item.call_back_id && setCallbackId(item.call_back_id)
        // }
        // if ((item?.status == '2' || item?.status == '8')) {
        //     setActiveRunningId(item.block_key)
        // }
    }

    // // 运行中 刷新页面 需要继续轮询
    // useEffect(() => {
    //     if (currentLoadingId) {
    //         // let outputId: any = Object.keys(blocks).find(item => blocks[item].type === 'output');
    //         // setActiveBlockId(outputId)
    //         startPolling(true, '')
    //         setRunTestModel('all');
    //         setIsRunTest(true);
    //         setFlowLoading(true);
    //     }
    // }, [currentLoadingId])
    // // 调试中 刷新页面 需要继续轮询
    // useEffect(() => {
    //     if (currentLoadingDebugId) {
    //         setActiveBlockId(currentLoadingDebugId)
    //         startPolling(false, currentLoadingDebugId)
    //         setRunTestModel('single');
    //         setIsRunTest(true);
    //         setBlockLoading(true);
    //     }
    // }, [currentLoadingDebugId])

    // 交互节点 continue_run
    const handleContinueRun = async (type = 'debug', outputs = {}) => {
        const activeBlockId = activeFormInteractionBlockId;
        if (type === 'debug' && !activeBlockId) return;

        // setBlockLoading(true);
        setFlowLoading(true);
        const res = await reqBlocksDetailContinueRun({
            template_id: chainId,
            block_index: activeBlockId,
            task_type: type === 'debug' ? 'debug_run' : 'flow_run',
            outputs,
        });
        if (type === 'debug') {
            if (res?.message === 'success') {
                // startPolling(activeBlockId);
            } else {
                // todo 
                // if((res?.message === 'error'))
                // message.success('调试失败')
                // blocks[activeBlockId].status = 4
                // setBlocks({ ...blocks })
                // setBlockLoading(false);
                // setFlowLoading(true);
            }
            return;
        }
        if (type === 'flow') {
            if (res?.message === 'success') {
                // todo 
                // setBlocks({ ...blocks })
                // startPolling();
            } else {
                // if((res?.message === 'error'))
                // message.error('运行失败')
                Object.keys(blocks).forEach(id => {
                    blocks[id].status = 4
                })
                // todo 
                // setBlocks({ ...blocks })
                // setFlowLoading(false);
                // setBlockLoading(false);
            }
        } else {
        }

    }

    // 单个flow的状态
    const getBlockDetail = async (id = '') => {
        const resId = id ? id : activeBlockId;

        if (!blockIds.length) {
            return false
        }

        const response: any = await reqBlockDetail({
            template_id: chainId,
            team_id: teamId,
            block_index: resId
        })
        if (!response) {
            setBlockLoading(false);
            setFlowLoading(false);
            clearInterval(pollTimer);
            return;
        }
        const res: any = response;
        console.log("🚀 ~ getBlockDetail ~ res:", res, response)
        let curRunNodeResultValue: any = {};
        const updateBlockData = (resId: string, res: any, isStop = false) => {
            // todo
            // blocks[resId].status = res.status
            // blocks[resId].result = res.result;
            // blocks[resId].inputs = res.inputs;
            // curRunNodeResultValue = {
            //     ...curRunNodeResultValue,
            //     [resId]: {
            //         status: res.status,
            //         result:  res.result || {},
            //         type: blocks[resId].type,
            //         inputs: res.inputs || {},
            //         is_end: runNodeResultValue[resId]?.is_end || false
            //     }
            // };
            // // todo 
            // setBlocks({ ...blocks })
            // if (isStop) {
            //     setBlockLoading(false);
            //     setFlowLoading(false);
            //     clearInterval(pollTimer);
            // }
        }

        if (res?.status == '7' && res?.type == 'form_interaction') {
            // 弹出交互节点
            setActiveFormInteractionBlockId(resId)
            setShowFormInteractive(true);
            setBlockLoading(false);
            setFlowLoading(false);
            clearInterval(pollTimer);
        }

        if (res?.status == '2' || res?.status == '3' || res?.status == '4') {
            if (response?.loop_blocks) {
                for (const [key, value] of Object.entries(response.loop_blocks)) {
                    // 更新循环体内节点
                    updateBlockData(key, value)
                }
            }

            // // 更新key_mapping值
            // let keyMapping: any = Object.keys(res) || [];
            // keyMapping && keyMapping.length && keyMapping.forEach((item: any) => {
            //     if (item.includes('key_mapping')) {
            //         blocks[resId][item] = res[item];
            //     }
            // });
            // 更新当前节点
            updateBlockData(resId, res, res?.status != '2');
            setRunNodeResultValue(
                {
                    ...runNodeResultValue,
                    ...curRunNodeResultValue
                }
            );
        }
        // if (res?.status == '2' && res?.type == 'loop') {
        //     if (response?.loop_blocks) {
        //         for (const [key, value] of Object.entries(response.loop_blocks)) {
        //             console.error(key, value)
        //             updateBlockData(key, value)
        //         }
        //     }
        //     // 更新循环节点result
        //     blocks[res.id].decodeResult = getResult(res.result)
        //     blocks[res.id].result = res.result;
        //     setBlocks({ ...blocks })
        // }
    }
    // 整个flow的状态
    const getBlocksDetail = async () => {
        let curIndex = 0;
        let isSetStopIndex = false

        const res = await reqBlocksDetail({
            template_id: chainId
        });
        console.error('flow all detail', res)
        let curRunNodeResultValue: any = {};
        res?.block_results.forEach((item: any, index: any) => {
            if (item?.status == '2' || item?.status == '3' || item?.status == '4' || item?.status == '5' || item?.status == '7') {
                let id = item.block_index;
                blocks[id].status = item.status;
                if (item?.status !== '5') {
                    blocks[id].result = item.block_result;
                    blocks[id].inputs = item.inputs;
                    blocks[id].is_end = item.is_end;
                    // 更新key_mapping值
                    // let curData = item.block_config;
                    // let keyMapping: any = Object.keys(curData) || [];
                    // keyMapping && keyMapping.length && keyMapping.forEach((itemKey: any) => {
                    //     if (itemKey.includes('key_mapping')) {
                    //         blocks[id][itemKey] = curData[itemKey];
                    //     }
                    // });
                }
                curRunNodeResultValue = {
                    ...curRunNodeResultValue,
                    [id]: {
                        status: item.status,
                        result: item.block_result || {},
                        type: blocks[id].type,
                        inputs: item.inputs || {},
                        is_end: item.is_end || false
                    }
                };
                // todo 
                // setBlocks({ ...blocks });
            }
        });
        setRunNodeResultValue(
            {
                ...runNodeResultValue,
                ...curRunNodeResultValue
            }
        );
        if (res?.flow_status == '3' || res?.flow_status == '4') {
            // 最后一项结束
            clearInterval(flowPollTimer);
            setStopLoading(false);
            setBlockLoading(false);
            setFlowLoading(false);
            // 运行完成 没有走到的分支手动置状态
            // blockIds.forEach((item, index) => {
            //     if (blocks[item].status == '2') {
            //         blocks[item].status = 8
            //     }
            // })
        }

        // 交互节点
        if (res?.block_events && res?.block_events.length) {
            let status = res?.block_events[0]?.status;
            if (status == '7') {
                // 弹出交互节点
                setActiveFormInteractionBlockId(res?.block_events[0]?.block_index);
                setBlockLoading(true);
                setFlowLoading(false);
                // 整体运行状态下，需要显示交互节点输入表单时，flow流整体运行按钮状态显示运行中
                if (runTestModel == 'all') {
                    setStopLoading(true);
                    setIsRunTestStatus(true);
                }
                setShowFormInteractive(true);
                clearInterval(flowPollTimer);

            }
        }
    }

    // 运行，调试， status 运行状态
    const getRunDebugFlowDetail = async (call_back_id = '') => {
        try {
            let res: any = await reqBlocksDetail({
                template_id: chainId,
                call_back_id
            });
            console.error('run debug flow detail', res);
            if (res) {
                if (res.flow_status != '2') {
                    clearInterval(flowPollTimer);
                    clearInterval(pollTimer);
                    setFlowLoading(false);
                    setBlockLoading(false);
                    setCurrentRunCallBackId('');
                }
                
                if (res?.block_results) {
                    
                    setRunNodeResultValue((prev: any) => {
                        let initObj = {...prev};
                        res?.block_results.forEach((item: any) => {
                            if (item?.block_index) {
                                let node_ref_val = {};
                                if (item?.node_ref) {
                                    Object.keys(item?.node_ref).forEach((nodeKey: any) => {
                                        if (blocks[nodeKey]) {
                                            node_ref_val[blocks[nodeKey].name] = item?.node_ref[nodeKey];
                                        }
                                    })
                                }
                                initObj[item?.block_index] = {
                                    ...initObj[item.block_index],
                                    ...item,
                                    inputs: {
                                        ...item.inputs,
                                        node_ref_val
                                    },
                                    result: item?.block_result,
                                    type: blocks[item.block_index].type,
                                    name: blocks[item.block_index].name,
                                }
                                // blocks[item.block_index].agent_id = item?.agent_id;
                            }
                        });
                        initObj.flow_status = res.flow_status;
                        return initObj;
                    });
                }

            } else {
                handleResetRunNodeErrorStatus();
            }
        } catch (error) {
            handleResetRunNodeErrorStatus();
        }
    }
    // 发布前的运行、调试
    const startPolling = (isAll = false, call_back_id = '') => {
        setCurrentRunCallBackId(call_back_id);
        // id为空代表all
        if (isAll) {
            if (isSse) {
                const onSuccessCb = (data: any) => {
                    // processBlockData(data)
                    console.log(data, '-------success')
                }
                const onOpenCb = (data: any) => {
                    console.log(data, '-------open')
                }
                const onFinishCb = (data: any) => {
                    // processBlockData(data);
                    processFinishFlow()
                    console.log(data, '-------finish')
                }
                const onFailCb = (data: any) => {
                    console.log(data, '-------fail')
                }
                getSSEFlowData(
                    {
                        
                    },
                    window.location.origin + '/api/flow/status/' + chainId,
                    {
                        onOpenCb,
                        onSuccessCb,
                        onFinishCb,
                        onFailCb
                    },
                    'GET'
                )
            } else {
                flowPollTimer = setInterval(() => {
                    getRunDebugFlowDetail(call_back_id)
                }, 3000);
            }
        } else {
            pollTimer = setInterval(() => {
                getRunDebugFlowDetail(call_back_id);
            }, 3000);
        }
    }

    // 发布后的运行入口
    const startPublishPolling = (log_id: any, index: any, isTeam = true, inputs: any) => {
        const onSuccessCb = (data: any) => {
            console.log(data, '-------success')

            if (data?.block_key) {
                processBlockData(data)
            } else {
                // 运行失败
                blockIds.forEach((item) => {
                    if (blocks[item].status == 2) {
                        blocks[item].status = 4
                    }
                })
                // todo 
                // setBlocks({ ...blocks })
                // setFlowLoading(false);
                // setBlockLoading(false);
            }
        }
        const onOpenCb = (data: any) => {
            console.log(data, '-------open')
        }
        const onFinishCb = (data: any) => {
            if (data?.done) {
                setActiveBlockId(blockIds[blockIds.length - 1])
                processFinishFlow()
            } else {
                processBlockData(data);
                processFinishFlow()
            }
            console.log(data, '-------finish')
        }
        const onFailCb = (data: any) => {
            console.log(data, '-------fail')
        }
        getSSEFlowData(
            {
                template_id: chainId,
                version: version,
                new_inputs: inputs
            },
            window.location.origin + '/api/flow/version/run_streams',
            {
                onOpenCb,
                onSuccessCb,
                onFinishCb,
                onFailCb
            },
            'POST'
        )
    }

    // 发布后运行
    const debugPublishFlow = async (isTeam = true, isVersion = false, value: any) => {
        const inputs: any = value;
        // blocks[inputBlockId].var_params.forEach((v: any) => {
        //     if (v.name) {
        //         inputs[v.name] = v.value
        //     }
        // })
        // blocks[inputBlockId].const_params.forEach((c: any) => {
        //     if (c.name) {
        //         inputs[c.name] = c.value
        //     }
        // })
        let res;
        if (isVersion) {
            // res = await reqRunVersionFlow({
            //     template_id: chainId,
            //     new_inputs: inputs,
            //     version
            // })
        } else if (isTeam) {
            res = await reqTestTeamPublishFlow({
                template_id: chainId,
                new_inputs: inputs
            })
        } else {
            res = await reqTestPublicPublishFlow({
                template_id: chainId,
                new_inputs: inputs
            })
        }

        if (res?.log_id || isVersion) {
            setLogId(res?.log_id || '')
            blockIds.forEach((item, index) => {
                if (index > 0) {
                    blocks[item].status = 2
                }
            })
            // todo 
            // setBlocks({ ...blocks })
            // setFlowLoading(true)
            // startPublishPolling(res?.log_id || '', 1, isTeam, inputs)
        } else {
            setBlockLoading(false);
            setFlowLoading(false);
        }
    }

    const [api, contextHolder] = notification.useNotification();

    const debouncedSaveFlow = debounce((edges:any, loopConfigs:any, blocks:any, isCheck:any) => {
        const resLoopConfigs = clone(loopConfigs)
        const loopKeys = Object.keys(resLoopConfigs)
        // 过滤没有起点 或者没有终点的边
        loopKeys.forEach((lid) => {
            if(!blocks[lid]){
                // 校验是否存在
                delete resLoopConfigs[lid]
            }
            resLoopConfigs[lid].edges = resLoopConfigs[lid].edges.filter((e: any) => {
                return e.sourceNodeID && e.targetNodeID && !!blocks[e.sourceNodeID] && !!blocks[e.targetNodeID]
            })
        })
         // 保存flow接口
        const p = {
            flowConfig: {
                blocks,
                // 过滤没有起点 或者没有终点的边 起点 终点也得存在blocks
                edges: edges.filter(e => e.sourceNodeID && e.targetNodeID && !!blocks[e.sourceNodeID] && !!blocks[e.targetNodeID]),
                loop_configs: resLoopConfigs,
            }
        } 
        reqSaveFlow({
            "template_id": chainId,
            "is_validate": isCheck,
            "flow_config": p.flowConfig,
        })
        console.log(blocks,p,'====PPPPsaveFlowPPPP===')

        setSaveTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'))

    }, 1000)
    // 保存flow
    const saveFlow =  useRecoilCallback(({ snapshot }) => async(isCheck = false, isShowMessage = false) => { 

        const blocks = await snapshot.getPromise(blocksAtom);
        const edges = await snapshot.getPromise(edgesAtom);
        const loopConfigs = await snapshot.getPromise(loopConfigsAtom);

        // 判断保存频率
        const curUpdateTime = new Date().getTime()
        if (curUpdateTime - updateTime < 2000) {
            // return;
        }
        updateTime = new Date().getTime()
        // 是否需要检查
        // console.log(isCheck, '----ischeck')

        // 已发布的不保存
        if (!isUnEdit && chainId) {
            debouncedSaveFlow(edges, loopConfigs, blocks, isCheck)
            // isShowMessage && message.success('保存成功');
        }
        
      },[chainId])
    // 重置未运行完的节点状态为4
    const handleResetRunNodeErrorStatus = (flow_status = 4) => {
        setRunNodeResultValue((prev: any) => {
            let initObj = {...prev};
            Object.keys(initObj).forEach((item: any) => {
                if (!['flow_status', 'query', 'start_time', 'exec_time'].includes(item)) {
                    let obj = {...initObj[item]};

                    if (flow_status == 6) {
                        obj.status = initObj[item].status ==  2 ? 6 : initObj[item].status;
                    } else {
                        obj.status = initObj[item].status ==  2 ? 4 : initObj[item].status;
                    }

                    initObj[item] =  obj;
                }
            });

            if (initObj?.flow_status != 3 && initObj?.flow_status != 4) {
                initObj.flow_status = flow_status;
            }
            
            let end_time = Math.ceil(Date.now() / 1000);
            initObj.exec_time = end_time - initObj.start_time;
            return initObj;
        });
        setCurrentRunCallBackId('');
        // clearInterval(flowPollTimer);
        // clearInterval(pollTimer);
        setFlowLoading(false);
        setBlockLoading(false);
    }

    // 运行，调试 sse
    const handleSseFlowDebugRun = async (type = 'all', params = {}) => {
        try {
            let start_time = Math.ceil(Date.now() / 1000);
            const onSuccessCb = (data: any) => {
                const {message, call_back_id, block_key} = data;
                if (message.status != 2) {
                    console.error('-------------success-----', data.message.status, data)
                }
                setCurrentRunCallBackId(call_back_id || '');
                setRunNodeResultValue((prev: any) => {
                    let initObj = {
                        ...prev,
                        flow_status: 2,
                    };
                    let prevData: any = {};
                    if (block_key) {
                        prevData = prev[block_key] || {};

                        initObj[block_key] = {
                            ...prevData,
                            ...message,
                            log_id: call_back_id,
                            block_index: block_key,
                            name: blocks[data?.block_key]?.name || '',
                            is_end: message?.execute_type === 'flow_end' ? true : false,
                            inputs: {
                                ...(prevData?.inputs || {}),
                                ...(message?.content?.inputs || {}),
                            },
                            result: {
                                ...(prevData?.result || {}),
                                ...(message?.content?.result || {}),
                            },
                            meta: {
                                ...(prevData?.meta || {}),
                                ...(blocks[block_key]?.meta || {}),
                                ...(message?.content?.meta || {}),
                            },
                            update_time_sort: Date.now()
                        }
                    }
                    let end_time = Math.ceil(Date.now() / 1000);
                    initObj.exec_time = end_time - initObj.start_time;
                     
                    return initObj;
                });
            }
            const onOpenCb = (data: any) => {
                console.log(data, '-------open')
                setRunNodeResultValue((prev: any) => {
                    let initObj = {
                        ...prev,
                        flow_status: 2,
                        start_time
                    };
                    return initObj;
                });
            }
            const onFinishCb = (data: any) => {
                console.log(data, '-------finish')
                if (!data.done && data?.errmsg) {
                    message.error(data?.errmsg);
                    handleResetRunNodeErrorStatus(4);
                }
                if (data?.done) {
                    handleResetRunNodeErrorStatus(3);
                }
            }
            const onFailCb = (data: any) => {
                console.log(data, '-------fail')
                handleResetRunNodeErrorStatus(4);
            }

            const onCancelCb = (data: any) => {
                console.log(data, '-------cancel')
                runFlowDebugCancelFun.current = data;
            }

            let url = '/api/flow/run/' + chainId;
            let method = 'POST';

            if (type === 'single') {
                url = '/api/flow/debug';
            }

            getSSEFlowData(
                {
                    ...params,
                    teamId: teamId
                },
                url,
                {
                    onOpenCb,
                    onSuccessCb,
                    onFinishCb,
                    onFailCb,
                    onCancelCb,
                },
                method,
                true
            )
        } catch (error) {
            handleResetRunNodeErrorStatus(4);
        }
    }


    // 调试 运行
    const debugBlock = async (isAll = false, runParams = {}) => {
        setCurrentRunCallBackId('');
        if (isAll) {
            // 重置全部节点状态
            setRunNodeResultValue((prev) => {
                let initObj = {...prev};
                Object.keys(initObj).forEach((item: any) => {
                    if (!['flow_status', 'query', 'start_time', 'exec_time'].includes(item)) {
                        initObj[item] = {
                            ...initObj[item],
                            status: 0,
                            is_end: false,
                        }
                    }
                });
                return {
                    ...initObj,
                    query: (runParams as any)?.query || '',
                    flow_status: 2,
                };
            });
            setFlowLoading(true)
        } else {
            //仅重置
            let initObj = {...runNodeResultValue};
            initObj = {
                ...initObj,
                [activeBlockId]: {
                    ...(initObj[activeBlockId] || {}),
                    status: 2,
                }
            }
            if (blocks[activeBlockId].type == 'nami_agent_loop') {
                // 循环体内的节点状态置为2
                loopConfigs[activeBlockId].all_block_keys.map((blockId: any) => {
                    initObj = {
                        ...initObj,
                        [blockId]: {
                            ...(initObj[blockId] || {}),
                            status: 0,
                        }
                    }
                });
            }
            setRunNodeResultValue(() => initObj)
            setBlockLoading(true)
        }

        // 保存flow
        await saveFlow();
        
        // 执行开启flow
        if (isAll) {
            handleSseFlowDebugRun('all', {
                run_params: runParams
            })
            // try {
            //     const res = await reqRunFlow({
            //         template_id: chainId,
            //         run_params: runParams
            //     })
            //     console.error('----运行------run--------', res)

            //     if (res && res?.call_back_id) {
            //         setCurrentRunCallBackId(res?.call_back_id || '');
            //         startPolling(true, res?.call_back_id);
            //     } else {
            //         // let msg = res?.message || '运行失败';
            //         // if (res?.data?.block_index && res?.data?.name) {
            //         //     msg = msg.replace('ID:', '');
            //         //     msg = msg.replace(res?.data?.block_index, res?.data?.name);
            //         //     message.error(msg);
            //         // }
            //         // 状态置为失败
            //         handleResetRunNodeErrorStatus();
            //     }
            // } catch (error) {
            //     handleResetRunNodeErrorStatus();
            // }
            
        } else {
            // 单节点调试
            // try {
            //     const res = await reqDebugFlow({
            //         template_id: chainId,
            //         block_index: activeBlockId,
            //         run_params: runParams
            //     })
            //     console.error('----调试------run--------', res)

            //     if (res && res?.call_back_id) {
            //         setCurrentRunCallBackId(res?.call_back_id || '');
            //         startPolling(true, res?.call_back_id);
            //     } else {
            //         // let msg = res?.message || '运行失败';
            //         // if (res?.data?.block_index && res?.data?.name) {
            //         //     msg = msg.replace('ID:', '');
            //         //     msg = msg.replace(res?.data?.block_index, res?.data?.name);
            //         //     message.error(msg);
            //         // }
            //         handleResetRunNodeErrorStatus();
            //     }
                
            // } catch (error) {
            //     handleResetRunNodeErrorStatus();
            // }
            handleSseFlowDebugRun('single', {
                template_id: chainId,
                block_index: activeBlockId,
                run_params: runParams
            })
        }
    }

    const getBodyKey = (data: any, mArray: any, label = '') => {
        data.forEach((d: any) => {
            if (d.children && d.children.length) {
                getBodyKey(d.children, mArray, label ? label + "/" + d.name : d.name)
            } else {
                if (d.name) {
                    mArray.push({
                        "source": [],
                        "target": label ? (label + "/" + d.name).split('/') : d.name,
                        "desc": d.description,
                        "type": d.type,
                        'required': d.required
                    })
                }
            }
        })
    }

    const copyPrompt = async () => {
        const res = await reqCopyVersionFlow({
            template_id: chainId
        })
        if (res) {
            message.success('复制成功')
            setFlowStatus('editable');
            setChainId(res.id)
            setTeamId(res.team_id)
            setPublishStatus(1)
            // 进入编辑态 清除version
            setVersion('')
        }
    }

    const checkFlow = async (isShowMessage = false) => {
        const res = await reqValidateFlow({
            template_id: chainId
        })
        // 初始化
        const keys = Object.keys(statusBlocks);
        keys.forEach((key: any) => {
            statusBlocks[key] = {}
        })
        setStatusBlocks({ ...statusBlocks })
        setActiveErrorId('')

        if (res) {
            if (res?.block_index) {
                // setActiveBlockId(res.block_index);
                // // blocks[res.block_index].status = 4
                // statusBlocks[res.block_index] = {};
                // statusBlocks[res.block_index].errorTip = res.err_message;
                // setStatusBlocks({ ...statusBlocks });
                // setActiveErrorId(res.block_index);
                // (flowRef.current as any)?.handleScrollToBlock(res.block_index);
                message.error(res.err_message)
            } else {
                setStatusBlocks({})
                setActiveErrorId('')
                isShowMessage && message.success('检查成功，可以运行。')
            }
        }
        setValidateLoading(false);
        return res;
    }
    // 编辑状态下，停止运行
    const handleStopEditRun = async (type = 'all') => {
        setStopLoading(true)
        let res;
        if (version) {
            let params:any = {
                 template_id: chainId,
                 // block_index: activeRunningId,
                 version,
                log_id: callbackId,
            };
            if (type && type === 'single') {
                params = {
                    ...params,
                    debug_type: type,
                };
            }
            res = await reqStopVersionFlow(params)
        } else {
            let params: any = {
                 template_id: chainId,
                 call_back_id: currentRunCallBackId
            }
            if (type && type === 'single') {
                params = {
                    ...params,
                    debug_type: type,
                };
            }
            res = await reqStopFlow(params)
        }
        if (res?.message === 'success') {
            console.log(runFlowDebugCancelFun.current)
            runFlowDebugCancelFun.current && runFlowDebugCancelFun.current?.cancel();
            handleResetRunNodeErrorStatus(6);
            setStopLoading(false);
             
            if (type && type === 'single') {
                getFlowDetail(false, false, activeBlockId);
            } else {
                getFlowDetail();
            }
            
        } else {
            setStopLoading(false);
        }
    }

    const onCloseFormRun = () => {
        // 如果节点运行中,关闭弹框停止当前运行状态
        console.log("🚀 ~ 如果节点运行中,关闭弹框停止当前运行状态", flowLoading, blockLoading, stopLoading)
        if ((stopLoading || blockLoading) && blocks?.[activeFormInteractionBlockId].type == 'form_interaction') {
            handleStopEditRun();
        }
        setShowFormInteractive(false);
    }

    const runBtnText = flowLoading ? (stopLoading ? '停止中' : '停止') : '运行'
    const isUnEdit = (flowStatus === 'addable' || flowStatus === 'uneditable') || (from === '1' || from === 'flow') || !!version

    const isVersion = !!version || (from === '1' || from === 'flow');

    return (
        <>
            <div style={{ minWidth: '1028px', width: '100%' }}>
                {contextHolder}
                <Spin spinning={initLoading || updateLoading} className='commonSpin'>
                    <div className={styles.top}>
                        <div className={styles.title}>
													<img src={leftBtn.src} className={styles.leftBtn} onClick={() => {
														if (backFuc) {
																backFuc()
														} else {
																if(isInNamiClient) {
																	handleFlowBack();
																} else if (from == 'flow') {
																		Router.push('/flowList?teamId=' + teamId)
																} else if (from !== undefined && from === "1") {
																		Router.push('/background/examineList')
																} else if (chainId && version) {
																		// 版本管理页面
																		Router.push(`operationDetailV3?id=${chainId}&type=flow&teamId=${teamId}&from=detail`)
																} else {
																		Router.push('/flowList?teamId=' + teamId)
																}
														}
													}} />
													<img src={promptImage || image.src} width={40} height={40} className={styles.imageWrapper} />
													<div>

															<span className={styles.nameWrapper}>{promptTitle}</span>
															{isUnEdit || isInNamiClient ? '' : <span className={styles.editBtn} onClick={() => {
																	!flowLoading &&  setIsEditNameModal(true);
															}}><img src={editBtn.src} alt="" width={16} /></span>}
															{saveTime ? <span className={styles.saveTime}>已自动保存 {saveTime}</span> : ''}
															<span className={styles.descWrapper} title={promptDesc}>{promptDesc}</span>
													</div>
                        </div>
                        <div className={styles.buttonWrapper}>
                            <div className={flowStyles.rightBtnWrapper}>
                                {/* flowStatus 
                                 uneditable: 不可编辑（对应【运行 、复制】 按钮） 
                                 editable: 可编辑 （对应【保存 运行 发布】按钮）
                                 addable: 可添加 （对应 【运行、添加】按钮） */}
                                {/* {(isUnEdit || !!version || (from === '1' || from === 'flow')) && <Button type='default' className={flowStyles.buttonWrapper + ' defaultButton'} disabled={blockLoading || flowLoading} onClick={() => { showLastResult() }}>{isShowLastResult ? '显示上次结果' : '隐藏上次结果'}</Button>} */}
                                {/* 查看版本 */}
                                {isVersion && <>
                                    <Tooltip title={'您可以修改开始中的变量值，测试其他运行结果。'}>
                                        <Button
                                            type='primary'
                                            className={'primaryButton'}
                                            disabled={blockLoading || stopLoading}
                                            loading={stopLoading}
                                            onClick={async () => {
                                                if (flowLoading) {
                                                    // 运行中
                                                    setStopLoading(true)
                                                    const res = await reqStopVersionFlow({
                                                        template_id: chainId,
                                                        // block_index: activeRunningId,
                                                        version,
                                                        log_id: callbackId
                                                    })
                                                    if (res?.message === 'success') {
                                                        // todo 
                                                        clearInterval(publishFlowPollTimer);
                                                        setStopLoading(false);
                                                        setBlockLoading(false);
                                                        setFlowLoading(false);
                                                        // getFlowDetail(false, true);
                                                        // blockIds.forEach((item) => {
                                                        //     if (blocks[item].status == 2) {
                                                        //         blocks[item].status = 6
                                                        //     }
                                                        // })
                                                        // setBlocks({ ...blocks })
                                                    } else {
                                                        setStopLoading(false);
                                                    }
                                                } else {
                                                    // debugPublishFlow(false, true)
                                                    // setIsShowSet(true);
                                                    // setActiveBlockId(lastBlockId)
                                                    // let timer = setTimeout(() => {
                                                    setRunTestModel('all');
                                                    setIsRunTest(true);
																										setIsUserFullScreen(false);
                                                    // clearTimeout(timer);
                                                    // });
                                                }
                                            }}>
                                            {runBtnText}
                                            {/* -- 查看version */}
                                        </Button>
                                    </Tooltip>
                                </>}

                                {/* 非外部组件 */}
                                {/* agent 新建 */}
                                {((!flowStatus || flowStatus === 'editable') && !version && from != "1") && <>
                                    {/* <Button type='default' className='defaultButton' onClick={() => { setOpenImportModal(true) }}>导入</Button> */}
                                    {/* 导入弹框 */}
                                    <Show
                                        when={openImportModal}
                                        children={<></>
                                            // <FlowDetailImportFile
                                            //     template_id={chainId}
                                            //     openImportModal={openImportModal}
                                            //     setOpenImportModal={setOpenImportModal}
                                            //     onFinish={() => { getFlowDetail(); getFlowPublishDetail() }}
                                            // />
                                        }
                                    />
                                    {/* <Button type='default' className='defaultButton' onClick={() => { flowExportJsonFile(chainId, promptTitle) }}>导出</Button> */}
                                    <Button type='default' className='defaultButton' disabled={flowLoading || blockLoading} onClick={() => {
                                        saveFlow(false, true);
                                        // 更新历史记录
                                        handleSavePromptHistory();
																				// 检查开始节点提示词自动插入user_input
																				checkAutoInsertUserInput(blocks, chainId, setBlocks);

                                        // 上传快照
                                        // if(isInNamiClient) {
                                        //     uploadSnapShot(document.querySelector('#canvas'), chainId)
                                        // }
                                    }}>保存
                                        {/* -- 编辑状态 */}
                                    </Button>

                                    {isUnEdit ?
                                        <Tooltip title={'您可以修改开始中的变量值，测试其他运行结果。'}>
                                            <Button
                                                type='default'
                                                className={'defaultButton'}
                                                disabled={blockLoading || stopLoading}
                                                loading={stopLoading}
                                                onClick={async () => {
                                                    if (flowLoading) {
                                                        // 运行中
                                                        setStopLoading(true)
                                                        const res = await reqStopTeamFlow({
                                                            template_id: chainId,
                                                            block_index: activeRunningId,
                                                            log_id: callbackId
                                                        })
                                                        if (res?.message === 'success') {
                                                            clearInterval(publishFlowPollTimer);
                                                            setStopLoading(false);
                                                            setBlockLoading(false);
                                                            setFlowLoading(false);
                                                            getFlowDetail(false, true);
                                                        } else {
                                                            setStopLoading(false);
                                                        }
                                                    } else {
                                                        // debugPublishFlow(true)
                                                        setIsShowSet(true);
                                                        setActiveBlockId(firstBlockId)
														setIsUserFullScreen(false)
                                                    }
                                                }}>
                                                {runBtnText}
                                            </Button>
                                        </Tooltip> :
                                        <Button
                                            type='default'
                                            className={'defaultButton'}
                                            disabled={blockLoading || stopLoading || (flowLoading && currentRunCallBackId === '')}
                                            loading={stopLoading}
                                            onClick={async () => {
                                                if (flowLoading) {
                                                    // 运行中
                                                    console.error('-------------edit---stop--')
                                                    handleStopEditRun('all');
                                                } else {
                                                    setRunTestModel('all');
                                                    setIsRunTest(true);
																										setIsUserFullScreen(false);
																										//检查开始节点提示词自动插入user_input
																										checkAutoInsertUserInput(blocks, chainId, setBlocks);
                                                }
                                            }}>{runBtnText}
                                            {/* -- 编辑状态 */}
                                        </Button>

                                    }
                                    {
                                        isInNamiClient ?
                                        <Button type='default' className='primaryButton' onClick={handleFlowBack}>
                                            退出
                                        </Button>
                                        :
                                        <Button type='primary' className='primaryButton' onClick={async () => {
																						// 检查开始节点提示词自动插入user_input
																						await checkAutoInsertUserInput(blocks, chainId, setBlocks);
                                            saveFlow();
                                            setIsShowSet(false);
                                            Router.push('./operationDetailV3?id=' + chainId + '&type=flow&teamId=' + teamId + '&from=detail&flowType=' + flowType);
                                        }}>发布</Button>
                                    }
                                </>}

                                {/* agent 打开详情 */}
                                {(flowStatus === 'uneditable') && <>
                                    <Button type='primary' className={'primaryButton'} disabled={flowLoading} onClick={() => copyPrompt()}>复制</Button>
                                </>}

                                {/* agent 添加 只能添加自己项目的flow */}
                                {(flowStatus === 'addable') && <>
                                    <Button type='primary' className={'primaryButton'} onClick={() => addFlow && addFlow()} disabled={flowLoading} >添加</Button>
                                </>}
                            </div>
                        </div>
                    </div>
                    <div className={flowStyles.container + (isShowSet ? '' : " " + flowStyles.showLeftContainer)}>
                        <div
                            id='reactFlowWrapper'
                            style={{ width: '100%', height: '100%' }}
                        >
                            {chainId && isInitData ?
                                <FlowEditor
                                    firstInitMATValue={firstInitMATValue}
                                    ref={flowRef}
                                    setFlowVersion={setFlowVersion}
                                    blocks={blocks}
                                    blocksPosition={blocksPosition}
                                    setBlocksPosition={setBlocksPosition}
                                    setBlocks={setBlocks}
                                    setActiveBlockId={setActiveBlockId}
                                    setIsShowSet={setIsShowSet}
                                    addBlock={addEditorBlock}
                                    deleteBlock={deleteBlock}
                                    activeBlockId={activeBlockId}
                                    blockIds={blockIds}
                                    blockLoading={blockLoading}
                                    flowLoading={flowLoading}
                                    chainId={chainId}
                                    teamId={teamId}
                                    flowType={flowType}
                                    cloneBlockItem={cloneBlockItem}
                                    setCloneBlockItem={setCloneBlockItem}
                                    isVersion={isVersion}
                                    setInitLoading={setInitLoading}
                                    isShowRight={isShowSet && activeBlockId && blockIds.length && !initLoading  && !isEditActive}
                                    isUserFullScreen={isUserFullScreen}
                                    saveFlow={saveFlow}
                                    clearStamp={clearStamp}
                                    setClearStamp={setClearStamp}
                                    setBlockIds={setBlockIds}
                                /> : ''}
                        </div>

                        { isUserFullScreen && isShowSet ?
                            <div className={flowStyles.fullScreenMask} onClick={ () => { setIsUserFullScreen(false) } }></div>
                            : null
                        }

                        {isShowSet && activeBlockId && blockIds.length && !initLoading  && !isEditActive?
                            <div id="blockDetail" className={flowStyles.right}>
                                <Resizable
                                    // defaultSize={{
                                    //     width: '440px',
                                    //     height: "auto"
                                    // }}
                                    size={{
                                        width: `${resizableWidth}px`,
                                        height: 'auto'
                                    }}
                                    onResizeStop={(e, direction, ref, delta) => { setResizableWidth(resizableWidth + delta.width) }}
                                    maxWidth="1000px"
                                    minWidth="440px"
                                    enable={{ top: false, right: false, bottom: false, left: true, topRight: false, bottomRight: false, bottomLeft: false, topLeft: false }}
                                    className={flowStyles.rightResizable}
                                >
                                    <div className={flowStyles.blockTitleWrapper}>
                                        <BlockTitle
                                            text={blocks[activeBlockId]?.name}
                                            meta={blocks[activeBlockId]?.meta}
                                            onSave={(newTitle) => {
                                                blocks[activeBlockId].name = newTitle;
                                                setBlocks(prev => ({...blocks}));
                                            }} />
                                        <div className={flowStyles.blockTitleButton} >
                                            {/* {(blocks[activeBlockId]?.type == 'input' && flowType == '1') || blocks[activeBlockId]?.type == 'set_variable' || blocks[activeBlockId]?.type == 'variable_merge' ? "" : <>
                                                <Tooltip title="调试">
                                                    {blockLoading || flowLoading ? <LoadingOutlined /> :
                                                        <img
                                                            className={flowStyles.blockTitleDebug}
                                                            onClick={() => {
                                                                if (isUnEdit) return;
                                                                setRunTestModel('single');
                                                                setIsRunTest(true);
                                                            }} src={debugSvg.src} alt="" />
                                                    }
                                                </Tooltip>
                                                <Divider type="vertical" />
                                            </>
                                            } */}
                                            <img onClick={() => { setIsShowSet(false) }} src={closeSvg.src} alt="" />
                                        </div>
                                    </div>
                                    {statusBlocks[activeBlockId] && statusBlocks[activeBlockId].errorTip ? <div className={flowStyles.errorWrapper}>
                                        <img src={errorTip.src} />
                                        <span className='normalFont'>{statusBlocks[activeBlockId].errorTip}</span>
                                    </div> : ''}
                                    <div className={flowStyles.blockContentWrapper} style={{
                                        height: statusBlocks[activeBlockId] && statusBlocks[activeBlockId].errorTip ? 'calc(100% - 108px)' : 'calc(100% - 48px)'
                                    }}>
										{blocks[activeBlockId]?.type === 'nami_agent' ? (<BlockAgent
                                            flowInfo={flowInfo}
                                            chainId={chainId}
                                            flowLoading={flowLoading}
                                            promptList={promptList}
                                            getType={getType}
                                            isWaiWang={isWaiWang}
                                            setOpenMarkDown={setOpenMarkDown}
                                            setContentValue={setContentValue}
                                            activeLlm={activeLlm}
                                            isShowLlmParam={isShowLlmParam}
                                            setIsShowLlmParam={setIsShowLlmParam}
                                            isUnEdit={isUnEdit}
                                            blocks={blocks}
                                            activeBlockId={activeBlockId}
                                            blockIds={blockIds}
                                            setBlocks={setBlocks}
                                            modelList={modelList}
                                            teamId={teamId}
                                            loopConfigs={loopConfigs}
                                            resizeWidth={setResizableWidth}
                                            isUserFullScreen={isUserFullScreen}
                                            setIsUserFullScreen={setIsUserFullScreen}
                                        />) : ''}

                                        {blocks[activeBlockId]?.type === 'nami_agent_condition' ? (<BlockAgentCondition
                                            flowInfo={flowInfo}
                                            isUnEdit={isUnEdit}
                                            blocks={blocks}
                                            activeBlockId={activeBlockId}
                                            setBlocks={setBlocks}
                                            modelList={modelList}
                                            chainId={chainId}
                                            getFlowDetail={getFlowDetail}
                                            
                                            flowLoading={flowLoading}
                                            promptList={promptList}
                                            setOpenMarkDown={setOpenMarkDown}
                                            setContentValue={setContentValue}
                                            activeLlm={activeLlm}
                                            isShowLlmParam={isShowLlmParam}
                                            setIsShowLlmParam={setIsShowLlmParam}
                                            teamId={teamId}
                                            loopConfigs={loopConfigs}
                                            resizeWidth={setResizableWidth}
                                            isUserFullScreen={isUserFullScreen}
                                            setIsUserFullScreen={setIsUserFullScreen}
                                        />) : ''}

                                        {blocks[activeBlockId]?.type === "nami_agent_loop" ? (<BlockIterationAgent
                                            flowInfo={flowInfo}
                                            chainId={chainId}
                                            flowLoading={flowLoading}
                                            promptList={promptList}
                                            getType={getType}
                                            isWaiWang={isWaiWang}
                                            setOpenMarkDown={setOpenMarkDown}
                                            setContentValue={setContentValue}
                                            activeLlm={activeLlm}
                                            isShowLlmParam={isShowLlmParam}
                                            setIsShowLlmParam={setIsShowLlmParam}
                                            isUnEdit={isUnEdit}
                                            blocks={blocks}
                                            activeBlockId={activeBlockId}
                                            blockIds={blockIds}
                                            setBlocks={setBlocks}
                                            modelList={modelList}
                                            teamId={teamId}
                                            loopConfigs={loopConfigs}
                                            resizeWidth={setResizableWidth}
                                            isUserFullScreen={isUserFullScreen}
                                            setIsUserFullScreen={setIsUserFullScreen}
                                        />) : ''}
                                    </div>
                                    {(blocks[activeBlockId]?.type == 'input' && flowType == '1') || blocks[activeBlockId]?.type == 'set_variable' || blocks[activeBlockId]?.type == 'variable_merge' ? "" : <div className={flowStyles.runDebugBox}>
                                        <div className={flowStyles.runDebugBoxContent} onClick={() => {
                                            if (isUnEdit) return;
                                            setRunTestModel('single');
                                            setIsRunTest(true);
																						setIsUserFullScreen(false);
                                        }}>
                                            <img src={ImgRunDebugPlay.src} />
                                            调试
                                        </div>
                                    </div>}
                                </Resizable>
                            </div> : ''}
                        {
                            isRunTest && runTestModel === 'single' ? <div id="blockDetail" className={flowStyles.right} style={{
                                width: resizableWidth + 'px'
                            }}>
                                <RunTestPage
                                    chainId={chainId}
                                    resizableWidth={resizableWidth}
                                    currentRunCallBackId={currentRunCallBackId}
                                    runNodeResultValue={runNodeResultValue}
                                    setRunNodeResultValue={setRunNodeResultValue}
                                    runTestModel={runTestModel}
                                    setIsRunTest={setIsRunTest}
                                    getType={getType}
                                    flowLoading={flowLoading}
                                    blockLoading={blockLoading}
                                    Editor={Editor}
                                    isUnEdit={isUnEdit}
                                    activeBlockId={activeBlockId}
                                    activeFormInteractionBlockId={activeFormInteractionBlockId}
                                    setActiveFormInteractionBlockId={setActiveFormInteractionBlockId}
                                    blocks={blocks}
                                    blockIds={blockIds}
                                    setBlocks={setBlocks}
                                    saveFlow={saveFlow}
                                    showFormInteractive={showFormInteractive}
                                    setShowFormInteractive={setShowFormInteractive}
                                    formInteractiveConfirm={formInteractiveConfirm}
                                    onCloseFormRun={onCloseFormRun}
                                    onRun={(value: any) => {
                                        if (isVersion) {
                                            debugPublishFlow(false, true, value)
                                        } else if (runTestModel === 'single') {
                                            debugBlock(false, value);
                                            // 更新历史记录
                                            handleSavePromptHistory();
                                        } else {
                                            // 整体运行
                                            debugBlock(true, value);
                                            handleSavePromptHistory();
																						// 检查开始节点提示词自动插入user_input
																						checkAutoInsertUserInput(blocks, chainId, setBlocks);
                                        }
                                    }}
                                    onStop={(type = 'all') => {
                                        handleStopEditRun(type);
                                    }}
                                />
                            </div> : ''
                        }
                        {
                            isRunTest && runTestModel === 'all' ? <div id="blockDetail" className={flowStyles.right} style={{
                                width: '480px'
                            }}>
                                <RunFlowPage
                                    flowInfo={flowInfo}
                                    chainId={chainId}
                                    currentRunCallBackId={currentRunCallBackId}
                                    runNodeResultValue={runNodeResultValue}
                                    setRunNodeResultValue={setRunNodeResultValue}
                                    runTestModel={runTestModel}
                                    setIsRunTest={setIsRunTest}
                                    getType={getType}
                                    flowLoading={flowLoading}
                                    blockLoading={blockLoading}
                                    Editor={Editor}
                                    isUnEdit={isUnEdit}
                                    activeBlockId={activeBlockId}
                                    activeFormInteractionBlockId={activeFormInteractionBlockId}
                                    setActiveFormInteractionBlockId={setActiveFormInteractionBlockId}
                                    blocks={blocks}
                                    blockIds={blockIds}
                                    setBlocks={setBlocks}
                                    saveFlow={saveFlow}
                                    showFormInteractive={showFormInteractive}
                                    setShowFormInteractive={setShowFormInteractive}
                                    formInteractiveConfirm={formInteractiveConfirm}
                                    onCloseFormRun={onCloseFormRun}
                                    onRun={(value: any) => {
                                        if (isVersion) {
                                            debugPublishFlow(false, true, value)
                                        } else {
                                            // 整体运行
                                            debugBlock(true, value);
                                            handleSavePromptHistory();
                                        }
                                    }}
                                    onStop={(type = 'all') => {
                                        handleStopEditRun(type);
                                    }}
                                />
                            </div> : ''
                        }
                    </div>
                </Spin>
            </div>


            <MarkDownModal
                openMarkDown={openMarkDown}
                setOpenMarkDown={setOpenMarkDown}
                contentValue={contentValue}
            />

            {blockMCPType.includes(blocks?.[activeBlockId]?.type || '') ? (
                <BlockAddMCP
                    open={openApi}
                    setOpen={setOpenApi}
                    blocks={blocks}
                    chainId={chainId}
                    blockId={activeBlockId}
                    blockType={blocks?.[activeBlockId]?.type || ''}
                    addedArr={blocks?.[activeBlockId]?.apis || []}
                />
            ) : (
                blockNamiType.includes(blocks?.[activeBlockId]?.type || '') ? (
                    blocks?.[activeBlockId]?.type === 'nami_agent' ? <BlockAddNmAgent
                        open={openApi}
                        setOpen={setOpenApi}
                        blocks={blocks}
                        chainId={chainId}
                        blockId={activeBlockId}
                        blockType={blocks?.[activeBlockId]?.type || ''}
                        addedArr={blocks?.[activeBlockId]?.apis}
                    /> : <BlockAddNmMcp
                        open={openApi}
                        setOpen={setOpenApi}
                        blocks={blocks}
                        chainId={chainId}
                        blockId={activeBlockId}
                        blockType={blocks?.[activeBlockId]?.type || ''}
                        addedArr={blocks?.[activeBlockId]?.apis}
                    />
                ) : ''
            )}

            {chainId ? <BlockAddFlow
                open={openAddFlow}
                setOpen={setOpenAddFlow}
                chainId={chainId}
                blockId={activeBlockId}
                blockType={blocks?.[activeBlockId]?.type || ''}
                addedArr={[blocks?.[activeBlockId]?.flow_plugin_id]}
                setChainId={setChainId}
                setFlowStatus={setFlowStatus}
                setFlowType={setFlowType}
            /> : ''}

            <BlockAddFlowGui
                open={openAddGui}
                setOpen={setOpenAddGui}
            />
            <CreateCommonModal
                pageName={'flow'}
                openModal={isEditNameModal}
                setOpenModal={(open: boolean) => {
                    setIsEditNameModal(open);
                }}
                okHandel={() => {
                    setIsEditNameModal(false);
                    getFlowDetail();
                }}
                baseInfo={{
                    id: chainId,
                    title: isMoreAgentTeams ? promptTitle.toString()?.replace('专家组队-', '') : promptTitle,
                    desc: promptDesc,
                    images: promptImage || image.src
                }}
            />

            {/* 使用引导按钮 */}
            {!isMoreAgentTeams && <TutorialGuide />}
        </>
    )
}
