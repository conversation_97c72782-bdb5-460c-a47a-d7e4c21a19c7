import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
	forwardRef,
	useImperativeHandle,
	useLayoutEffect
} from 'react'
import { useRecoilValue, useRecoilState } from'recoil'
import { Editor, Transforms, Range, createEditor } from 'slate'
import { withHistory } from 'slate-history'
import {
  Editable,
  ReactEditor,
  Slate,
  useFocused,
  useSelected,
  withReact,
} from 'slate-react'
import ReactDOM from'react-dom'
import { Popover, Button } from 'antd'
import PromptMentionMenu from './promptModal/PromptMentionMenu'
import PromptMentionMenuPopover from './promptModal/PromptMentionMenuPopover'
import { reqGetNmAgentMcpDetailList } from "@/service/flow3.0";
import { useRouter } from 'next/router'
import { checkAndUpdateMention, getAllPrevBlocks } from '@/components/flow3.0/flowFunc'
import {
	edgesAtom,
} from "@/atoms/flowEditorAtoms";
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import styles from './styles/MentionEditor.module.scss'

import userInputSvg from '@/images/flow3.0/userInput.svg'
import agentOutputSvg from '@/images/flow3.0/agentOutput.svg'
import mcpSvg from '@/images/flow3.0/mcp.svg'
import extendIcon from '@/images/flow3.0/extend.svg';
import shrinkIcon from '@/images/flow3.0/shrink.svg';
import errorTipIcon from '@/images/flow3.0/errorTip.svg'
import { deepClone } from '@/utils/utils'
import { mcpToolListAtom } from '@/atoms/flowAtoms'
import TemplatePlaceholder from './TemplatePlaceholder'
import {  TemplateSelect } from './TemplateElement'
import { isEqual } from 'lodash'

let timerId

const Portal = ({ children }) => {
  return typeof document === 'object'
    ? ReactDOM.createPortal(children, document.body)
    : null
}

interface MentionEditorProps {
		blocks: any,
		loopConfigs: any,
		activeBlockId: string,
		isFullScreen: boolean,
		setIsFullScreen: (value: boolean) => void,
		onChange: (value: any) => void,
		resizeWidth: (value: any) => void,
	}

const MentionEditor = forwardRef(
	(props: MentionEditorProps, myRef) => {
	const { blocks, activeBlockId, loopConfigs, onChange, isFullScreen, setIsFullScreen, resizeWidth } = props
	const router = useRouter()
	const [ isPopoverOpen, setIsPopoverOpen ] = useState(false)
  const ref = useRef(null)
  const [target, setTarget] = useState(null)
	const [ lastSection, setLastSection ] = useState()
	// const [mcpToolList, setMcpToolList] = useState([])
	const [ mcpToolList, setMcpToolList ] = useRecoilState(mcpToolListAtom)
	const [menuHeight, setMenuHeight] = useState(0)
	const [ isFocused, setIsFocused ] = useState(false)
  const renderElement = useCallback(props => <Element {...props} onCustomEvent={handleCustomEvent} />, [])
  const renderLeaf = useCallback(props => <Leaf {...props} />, [])
  const editor = useMemo(
    () => withMentions(withReact(withHistory(createEditor()))),
    []
  )


	const atomEdges = useRecoilValue(edgesAtom)
	const initialValue = useMemo(() => {
		console.log(`useMemo`)
		const result = (blocks[activeBlockId].prompts?.user_params?.length > 0 && blocks[activeBlockId].prompts.user_params)
					||
					[
						{
							type: 'paragraph',
							children: [{ text: '' }]
						}
					]
		console.log(`useMemo result: `, result)
		return result
	}, [ blocks, activeBlockId ])

	useEffect(() => {
		if(blocks[activeBlockId]?.prompts?.user_params?.length > 0) {
			const prevBlocks = getAllPrevBlocks(activeBlockId, blocks, atomEdges, loopConfigs)
			let user_params = checkAndUpdateMention(blocks, activeBlockId, prevBlocks, loopConfigs, mcpToolList)
			editor.children = user_params
		}
	}, [ blocks, activeBlockId, atomEdges, loopConfigs, mcpToolList ])

	const editableRef = useRef(null)

	// 创建观察者实例
	const resizeObserver = new ResizeObserver(entries => {
		for (const entry of entries) {
			// 获取当前宽度（contentRect 包含元素的宽高信息）
			const height = entry.contentRect.height;
			console.log('元素宽度变化为：', height);
			setMenuHeight(height)
		}
	});

	const handleMentionSelect = (value) => {
		if(target) {
			//插入到光标位置
      if(target?.path) {
        // 不处理光标
      } else {
        Transforms.select(editor, target)
      }
		} else if (lastSection){
			//插入到上次选中的位置
			Transforms.select(editor, lastSection)
		} else {
			//插入到文档末尾
			Transforms.select(editor, Editor.end(editor, []))
		}

		insertMention(editor, value, target?.path)
		// 插入后自动聚焦
		if (editableRef.current) {
			editableRef.current.focus();
		}
		setTarget(null)
	}

	const initMcpToolList = async () => {
		const server_name = blocks[activeBlockId].mcp_list?.map(item => item.server_name).join(',')
		console.log('mcpToolList', server_name)
		if(!server_name) {
			return setMcpToolList([])
		}
		const res = await reqGetNmAgentMcpDetailList({ 
			server_name,
			block_key: activeBlockId,
			flow_id: router.query.id
		})
		setMcpToolList(res)
	}

	// 重置编辑器内容
	const handleReset = (value) => {
		const children = [...editor.children];
		//删除旧内容
		children.forEach((_, index) => {
			Transforms.removeNodes(editor, { at: [0] });
		})
		// 插入新内容
		value.forEach((node) => {
			Transforms.insertNodes(editor, node);
		});
	};

	// 处理输入法输入
	const handleCompositionStart = () => {
		timerId && clearTimeout(timerId)
	}

  // 获取当前选区位置
  const getSelectionPosition = () => {
    // 1. 获取 Slate 编辑器当前的选区
    const selection = editor.selection;
    console.log(selection, 'selection')

    if (!selection) {
      console.log('没有选区，光标未定位');
      return null;
    }

    // 2. 解析选区的起始点（anchor）和结束点（focus）
    // const { anchor, focus } = selection;
    setTarget(selection)
  }

	//处理自定义事件
	const handleCustomEvent = (eventData) => {
		console.log(`接收自定义元素事件`, eventData)

		if(eventData.type === 'showMentionMenu') {
      if(eventData.target) {
        const path = ReactEditor.findPath(editor, eventData.element);
        setTarget({ path, element: eventData.element, type: "inputTrigger"})
      } else {
        getSelectionPosition()
      }
		}
	}

	/*======================useEffect=====================*/
	useEffect(() => {
		const el = ref.current
    if (target && el) {
      try {
        let domRange = null;
        if(target?.anchor){
          domRange = ReactEditor.toDOMRange(editor, target)
        } else if(target?.element) {
          domRange = ReactEditor.toDOMNode(editor, target.element)
        }
        const rect = domRange?.getBoundingClientRect()
				const windowHeight = window.innerHeight || document.documentElement.clientHeight
				let targetTop = rect.top + window.pageYOffset + 24
				let targetLeft = rect.left + window.pageXOffset - 10
				let diffY = rect.top + 24 + menuHeight - windowHeight
				if(diffY > 0) {
					targetTop = targetTop - diffY
					targetLeft = targetLeft - 266
				} else {
					targetLeft = targetLeft - 180
				}
				el.style.top = `${targetTop}px`
				el.style.left = `${targetLeft}px`
				
				resizeObserver.observe(el)
			} catch (e) {
				console.log(`error: ${e}`)
			}
    }
		return () => {
			el && resizeObserver.unobserve(el)
		}
  }, [editor, target, menuHeight])

	useEffect(() => {
		initMcpToolList()
	}, [ blocks[activeBlockId].mcp_list, activeBlockId ])

	useEffect(() => {
		const clientWidth = window.innerWidth || document.documentElement.clientWidth
		resizeWidth(isFullScreen? clientWidth * 0.6 : 440)
	}, [ isFullScreen ])

	const handleClickOutside = (event) => {
		if (editableRef.current && !editableRef.current.contains(event.target) && (ref.current &&!ref.current.contains(event.target))) {
			setTarget(null)
		}
	}

  const handleClickEditor = () => {
    const { selection } = editor
    if (selection && Range.isCollapsed(selection)) {
      let [start] = Range.edges(selection)
      const wordBefore = Editor.before(editor, start, { unit: 'character' })
      // const before = wordBefore && Editor.before(editor, wordBefore)
      const beforeRange = wordBefore && Editor.range(editor, wordBefore, start)
      const beforeText = beforeRange && Editor.string(editor, beforeRange)
      const beforeMatch = beforeText && beforeText.match(/\/$/)
      if (!beforeMatch ) {
        setTarget(null)
      }
  }
}

	useEffect(() => {
		document.addEventListener('click', handleClickOutside, false)
		return () => {
			document.removeEventListener('click', handleClickOutside, false)
		}
	}, [ editableRef, ref ])

	/*======================useImperativeHandle=*/
	useImperativeHandle(myRef, () => ({
		handleReset,
		editor
	}), [ editor ])
	
  return (
		<div className={[styles.container, isFocused && styles.focused].join(' ')}>
			{/* <div className={styles.operate}>
				<PromptMentionMenuPopover
					type="userInput"
					onSelect={handleMentionSelect}
					blocks={blocks}
					loopConfigs={loopConfigs}
					mcpToolList={mcpToolList}
					activeBlockId={activeBlockId}
					placement={"bottomLeft"}
					handleOpenChange={(isOpen: boolean) => setIsPopoverOpen(isOpen)}
				>
					<Button color="default" variant="outlined" className={styles.operateBtn}>
						<img className={styles.operateBtnIcon} src={userInputSvg.src} alt="" />
						用户输入文件
					</Button>
				</PromptMentionMenuPopover>
				<PromptMentionMenuPopover
					type="agentOutput"
					onSelect={handleMentionSelect}
					blocks={blocks}
					loopConfigs={loopConfigs}
					mcpToolList={mcpToolList}
					activeBlockId={activeBlockId}
					handleOpenChange={(isOpen: boolean) => setIsPopoverOpen(isOpen)}
				>
					<Button color="default" variant="outlined" className={styles.operateBtn}>
						<img className={styles.operateBtnIcon} src={agentOutputSvg.src} alt="" />
						智能体输出文件
					</Button>
				</PromptMentionMenuPopover>
				<PromptMentionMenuPopover
					type="mcp"
					onSelect={handleMentionSelect}
					blocks={blocks}
					loopConfigs={loopConfigs}
					mcpToolList={mcpToolList}
					activeBlockId={activeBlockId}
					placement="bottomRight"
					handleOpenChange={(isOpen: boolean) => setIsPopoverOpen(isOpen)}
				>
					<Button color="default" variant="outlined" className={styles.operateBtn}>
						<img className={styles.operateBtnIcon} src={mcpSvg.src} alt="" />
						MCP工具
					</Button>
				</PromptMentionMenuPopover>
			</div> */}
			<Slate
				editor={editor}
				initialValue={initialValue}
				onChange={(value) => {
					timerId && clearTimeout(timerId)
					timerId = setTimeout(() => {
						if(!isEqual(value, blocks[activeBlockId].prompts.user_params)) {
							console.log('onValueChange', value)
							//TODO 暂时先用deepClone，后续优化
							onChange(deepClone(value))
						}
					}, 500)
					const { selection } = editor
          console.log(`selection`, selection)
					setLastSection(selection)
					if (selection && Range.isCollapsed(selection)) {
						let [start] = Range.edges(selection)
						const wordBefore = Editor.before(editor, start, { unit: 'character' })
						// const before = wordBefore && Editor.before(editor, wordBefore)
						const beforeRange = wordBefore && Editor.range(editor, wordBefore, start)
						const beforeText = beforeRange && Editor.string(editor, beforeRange)
						const beforeMatch = beforeText && beforeText.match(/\/$/)
						console.log(`beforeMatch:${beforeMatch}`)

						const after = Editor.after(editor, start)
						const afterRange = Editor.range(editor, start, after)
						const afterText = Editor.string(editor, afterRange)
						const afterMatch = afterText.match(/^(\s|$)/)

						if (beforeMatch ) {
							setTarget(beforeRange)
							return
						}
					}
          // 标记是否点击 select 节点
          if(target?.path) {
            return
          }
					setTarget(null)
				}}
			>
				<Editable
					ref={editableRef}
					renderElement={renderElement}
					renderLeaf={renderLeaf}
					onFocus={() => { setIsFocused(true) }}
					onBlur={() => { setIsFocused(false) }}
					onCompositionStart={handleCompositionStart}
					placeholder={'在这里写你的提示词，输入“/” 绑定“用户输入、智能体输出、已配置MCP工具、用户界面”'}
          onClick={handleClickEditor}
					style={{ 
						fontSize: '14px', 
						padding: '5px 12px',
						outline: 'none',
					}}
					renderPlaceholder={(props) => {
							// 自定义占位符渲染
							return (
								<span
									{...props}
									style={{
										color: '#9EA7B8',
										fontSize: '14px',
										position: 'absolute',
										top: '6px',
										left: '12px',
										right: '12px',
										pointerEvents: 'none',
									}}
								/>
							);
						}}
				/>
				{target && (
					<Portal>
						<div
							ref={ref}
							style={{
								top: '-9999px',
								left: '-9999px',
								position: 'absolute',
								zIndex: 10000,
								padding: '3px',
								background: 'white',
								borderRadius: '12px',
								boxShadow: '0 1px 5px rgba(0,0,0,.2)',
							}}
							data-cy="mentions-portal"
						>
							<PromptMentionMenu
                type={target?.type}
								onSelect={ handleMentionSelect }
								blocks={ blocks }
								activeBlockId= { activeBlockId }
								loopConfigs={loopConfigs}
								mcpToolList={mcpToolList}
							/>
						</div>
					</Portal>
				)}
			</Slate>
		</div>
  )
}
)
const withMentions = editor => {
  const { isInline, isVoid, markableVoid } = editor
  const inlineList = ["mention", "template-placeholder", "template-select", "template-input"];
  const voldList = ["mention"]

  editor.isInline = element => {
    return inlineList.includes(element.type) ? true : isInline(element)
  }
  editor.isVoid = element => {
    return voldList.includes(element.type) ? true : isVoid(element)
  }
  editor.markableVoid = element => {
    return inlineList.includes(element.type) || markableVoid(element)
  }
  return editor
}

const insertMention = (editor, value, path) => {
	// console.log(`insertMention:`, value)
  const mention = {
    type: 'mention',
    payload: {
				name: value.name,
				id: value.id,
				type: value.type,
				icon: value.icon,
				color: value.color,
				server_name: value.server_name,
				hasError: value.hasError
		},
    children: [{ text: '' }],
  }
  Transforms.insertNodes(editor, mention, path ? {at: path} : undefined)
  Transforms.move(editor)
}

const Leaf = ({ attributes, children, leaf }) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>
  }
  if (leaf.code) {
    children = <code>{children}</code>
  }
  if (leaf.italic) {
    children = <em>{children}</em>
  }
  if (leaf.underline) {
    children = <u>{children}</u>
  }
  return <span {...attributes}>{children}</span>
}

const Element = (props) => {
	// console.log(`Element: }`, props)
  const { attributes, children, element, onCustomEvent } = props
  switch (element.type) {
    case 'mention':
      return <Mention {...props} />
    case 'heading':
      return (
        <h3 {...attributes} style={{
          fontSize: '14px',
          fontWeight: '500',
          color: '#387F8A',
          margin: '12px 0 8px 0'
        }}>
          {children}
        </h3>
      )
    case 'template-input':
      return (
        <span {...attributes} style={{
          fontSize: '14px',
          fontWeight: '500',
          color: '#387F8A',
          margin: '12px 0 8px 0'
        }}>
          {children}
        </span>
      )
    case 'template-placeholder':
      return <TemplatePlaceholder {...props} />
    case 'template-select':
      return <TemplateSelect {...props} onCustomEvent={onCustomEvent} />
    default:
      return <p {...attributes}>{children}</p>
  }
}

const Mention = (props) => {
	const { attributes, children, element } = props
  const selected = useSelected()
  const focused = useFocused()
	const { payload } = element
	const myRef = useRef(null)
	const [ isTipShow, setIsTipShow ] = useState(false)
  const style: React.CSSProperties = {
    padding: '4px 8px',
    margin: '2px',
    display: 'inline-block',
    borderRadius: '4px',
    fontSize: '12px',
    boxShadow: selected && focused ? '0 0 0 2px #B4D5FF' : 'none',
		position: 'relative',
    fontWeight: 400
  }
	const { hasError } = payload
	const bgColor = hasError ? '#FFF5F2' : '#F0F2FF'
	const color = hasError ? '#F53F3F' : '#006BFF'
	style.color = color
	style.backgroundColor = bgColor

	const handleMouseEnter = (e) => {
		if(e.currentTarget.classList.contains('mention-error')) {
			const { left, top, width, height } = e.currentTarget.getBoundingClientRect()
			myRef.current.style.left = `${(width - myRef.current.offsetWidth) / 2 + left}px`
			myRef.current.style.top = `${top - height - 10}px`
			myRef.current.style.opacity = 1;
			setIsTipShow(true)
		}
	}

	const handleMouseLeave = () => {
		myRef.current.style.left = '-9999px'
		myRef.current.style.top = '-9999px'
		myRef.current.style.opacity = 0;
		setIsTipShow(false)
	}

	const handleScroll = () => {
		if(isTipShow && myRef.current) {
			handleMouseLeave()
		}
	}

	useEffect(() => {
		window.addEventListener('scroll', handleScroll, true)

		return () => {
			window.removeEventListener('scroll', handleScroll, true)
		}
	}, [ isTipShow ])

  return (
    <span
			className={ hasError ? "mention-error" : "" }
      {...attributes}
      contentEditable={false}
			data-mention={true}
			data-mention-type={payload.type}
			data-mention-id={payload.id}
			data-mention-servername={payload.server_name}
      style={style}
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
    >
			{/* <div
			  contentEditable={false}
				style={{
					display: 'inline-block',
					borderRadius: '4px',
					marginRight: '4px',
					verticalAlign: 'middle',
					textAlign: 'center',
					width: '16px',
					height: '16px',
					lineHeight: '16px',
					backgroundColor: payload.color ? payload.color : '',
				}}
			>
				<img
					src={payload.icon}
					style={{
						width: payload.type === 'mcp'? '14px': '10px',
					}}
					alt=""
				/>
			</div> */}
			{
				hasError && (
					<img
						src={errorTipIcon.src} 
						style={{
							width: '16px',
							marginRight: '4px',
							verticalAlign: 'middle'
						}}
						alt="" 
					/>
				)
			}
      <span
				contentEditable={false}
				style={{
					color: color,
					verticalAlign: 'middle'
				}}
			>
				{payload.name}
      </span>
			<Portal>
				<div
					ref={myRef}
					className={styles.errorTipPopover}
				>
					不存在引用关系
				</div>
			</Portal>
    </span>
  )
}

export default MentionEditor