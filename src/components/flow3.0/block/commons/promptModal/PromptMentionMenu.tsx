import { useEffect, useState, useRef } from 'react'
import { useRecoilValue } from'recoil'
import {
	edgesAtom
} from "@/atoms/flowEditorAtoms";
import { getAllPrevBlocks, isInLoop, getLoopGroupBlocks } from '../../../flowFunc'
import { iconList } from '@/components/flowEditor3.0/flowConfig'
import styles from './PromptMentionMenu.module.scss'
import userInputIcon from '@/images/flow3.0/userInputBlack.svg'
import agentOutputIcon from '@/images/flow3.0/agentOutputBlack.svg'
import mcpIcon from '@/images/flow3.0/mcpBlack.svg'
import arrowRight from '@/images/arrowRight.png'
import chatIcon from '@/images/flow3.0/chat.svg'
import guiIcon from '@/images/flow3.0/guiBlack.svg'
import { Popover } from 'antd';

const defaultData = {
	"userInput": {
		title: "用户输入",
		icon: userInputIcon.src,
		list: [
			{
				name: "用户输入",
				icon: chatIcon.src,
				color: '#64B5F6'
			}
		]
	},
	"agentOutput": {
		title: "引用智能体输出",
		icon: agentOutputIcon.src,
		list: []
	},
	"mcp": {
		title: "引用MCP工具",
		icon: mcpIcon.src,
		list: []
	},
	"knowledge": {
		title: "引用知识库",
		list: []
	},
	"gui": {
		title: "用户界面",
		icon: guiIcon.src,
		list: []
	}
}

const MenuGroupItem = (props) => {
	const { item, subItem, type, onSelect, handleOpenChange } = props;
	const [ isTruncated, setIsTruncated ] = useState(false)
	const ref = useRef(null)

	useEffect(() => {
		if(ref.current) {
			const isEllipsis = ref.current.scrollWidth > ref.current.clientWidth
			setIsTruncated(isEllipsis)
		}
	}, 
	[
		subItem.name_ui, 
		subItem.name
	])

	const getListItem = () => {
		return <li
			ref={ref}
			className={ styles.menuGroupItemContentItem }
			onClick={ (e) => {
				e.stopPropagation()
				handleOpenChange && handleOpenChange(false)
				if(type === 'agentOutput') {
					onSelect({
						...subItem,
						type: type
					})
				} else if (type === 'mcp') {
					onSelect({
						...item,
						id: subItem.name,
						type: type,
						name: `${item.name}-${subItem.name_ui}`
					})
				} else if ( type === 'gui') {
					onSelect({
						...item,
						id: subItem.name,
						type: 'mcp',
						name: `${item.name}-${subItem.name_ui}`
					})
				}
			}}
		>
			{ type === 'mcp' || type === 'gui' ? subItem.name_ui : subItem.name }
		</li>
	}

	return (
		<li>
			{
				isTruncated ?
				<Popover
					content={ <div style={{ padding: "4px 8px" }}>{ type === 'mcp' || type === 'gui' ? subItem.name_ui : subItem.name }</div> }
					trigger="hover"
					placement="top"
					overlayStyle={{ zIndex: 10001 }}
				>
					{ getListItem() }
				</Popover>
				:
				getListItem()
			}
		</li>
	)
}

const MenuGroup = (props) => {
	const { data, type, onSelect, showEmpty, handleOpenChange } = props;
	const [ list, setList ] = useState(data.list)

	useEffect(() => {
		setList(data.list)
	}, [ data.list ])

	const handleToggle = (item) => {
		setList(preList => {
			const newList = preList.map(n => 
				n.server_id === item.server_id ?
				{ ...n, _isOpen: !n._isOpen }
				:
				n
			)
			return newList
		})
	}

	if(!list.length && !showEmpty) {
		return null
	}
	
	return (
		<>
			<div className={styles.menuGroup}>
				<div className={styles.menuGroupTitle}>
					<img className={styles.menuGroupTitleIcon} src={data.icon} alt="" />
					{ data.title }
				</div>
				{ 
					!list.length ?
					<div className={styles.menuGroupEmpty}>
						<img className={styles.menuGroupEmptyImg} src="https://p0.ssl.qhimg.com/t11098f6bcd45ab55c0a5737240.png" alt="" />
						<span className={styles.menuGroupEmptyText}>暂无数据</span>
					</div>
					:
					list.map((item, index) => (
							<div className={styles.menuGroupList}>
								<div 
									className={styles.menuGroupItem} 
									onClick={() => {
										handleOpenChange && handleOpenChange(false)
										onSelect({
											...item,
											type: (type === 'agentOutput' && item.inLoop) ? 'iteratorItem' : (type === 'gui' ? 'mcp': type),
										})
									}} 
								>
									<div className={styles.menuGroupItemHeader}>
										<div 
											className={styles.menuGroupItemIconWrap}
											style={{ background: item.color || '' }}
										>
											<img
												style = {{ width: (type === 'agentOutput' || type === 'userInput') ? '15px' : '100%' }}
												src={ item.icon }
												alt=""
											/>
										</div>
										<span
											className={styles.menuGroupItemHeaderName}
										>{ item.name }</span>
										{
											item.children && item.children.length > 0 && 
											<div 
												className={styles.menuGroupItemArrowWrap}
												onClick={(e) => {
													e.stopPropagation()
													handleToggle(item)
												}} 
											>
												<img 
													src={arrowRight.src} 
													className={[styles.menuGroupItemArrow, item._isOpen ? styles.menuGroupItemArrowOpen : ''].join(' ')} 
													
													alt="" 
												/>
											</div>
										}
									</div>

										{
											  item._isOpen && item.children?.length > 0 && (
												<ul className={ styles.menuGroupItemContent } key={index}>
														{
															 item.children.map((subItem) => 
																<MenuGroupItem 
															 		key={subItem.id}
																	item={item}
															 		subItem={subItem}
																	type={type}
																	onSelect={onSelect}
																	handleOpenChange={handleOpenChange}
																/>
															)
														}
												</ul>
											)
										}
									</div>
							</div>
					))
				}
			</div>
		</>
	)
}

const PromptMentionMenu = (props) => {
	const { type = "all", onSelect, blocks, activeBlockId, loopConfigs, mcpToolList, handleOpenChange, target } = props;
	const [ data, setData ] = useState({...defaultData})
	const atomEdges = useRecoilValue(edgesAtom)
	
	const initData = () => {
		const prevBlocks = getAllPrevBlocks(activeBlockId, blocks, atomEdges, loopConfigs)
		const mcpList = mcpToolList?.map((item) => ({
			...item,
			name: item.display_name,
			icon: item.ext.icon,
			children: item.tools
		})) || []
		data.mcp.list = mcpList.filter(item => item.server_name != 'custom-ui-card')
		data.gui.list = mcpList.filter(item => item.server_name == 'custom-ui-card')
		data.agentOutput.list = prevBlocks.map((item) => ({ 
			name: item.name, 
			id: item.id, 
			icon: iconList[item.meta.iconIndex].image.src, 
			color: item.meta.color,
			children: item.type === 'nami_agent_loop'? getLoopGroupBlocks(loopConfigs, item.id, blocks)?.map(blockKey => {
				return {
					name: blocks[blockKey].name,
					id: blocks[blockKey].id,
					icon: iconList[blocks[blockKey].meta.iconIndex].image.src,
					color: blocks[blockKey].meta.color,
				}
			}) : []
		}))
		//判断是否在迭代节点里面
		const inLoop = isInLoop(activeBlockId, blocks)
		console.log(`inLoop:${inLoop}`)
		if(inLoop && blocks[activeBlockId].type != 'nami_agent_loop') {
			const block = blocks[blocks[activeBlockId].loop_id]
			data.agentOutput.list.unshift({
				name: `${block.name}-迭代项`,
				id: block.id,
				icon: iconList[block.meta.iconIndex].image.src,
				color: block.meta.color,
				inLoop: true,
			})
		}

		setData({ ...data })
	}

	useEffect(() => {
		initData()
	}, [ mcpToolList, activeBlockId ])

	return (
		<div className={styles.menu}>
			{
				type === "all" ?
				Object.keys(data).map(key => {
					return <MenuGroup
						key={key}
						data={data[key]}
						type={key}
						onSelect={onSelect}
						showEmpty={false}
						handleOpenChange={handleOpenChange}
					/>
				})
				:
				(
					type === "inputTrigger" ?
					["userInput", "agentOutput"].map(
						key => {
							return <MenuGroup
								key={key}
								data={data[key]}
								type={key}
								onSelect={onSelect}
								showEmpty={false}
								handleOpenChange={handleOpenChange}
							/>
						}
					)
					:
					<MenuGroup
						data={data[type]}
						type={type}
						onSelect={onSelect}
						showEmpty={true}
						handleOpenChange={handleOpenChange}
					/>
				)
			}
		</div>
	)
}

export default PromptMentionMenu