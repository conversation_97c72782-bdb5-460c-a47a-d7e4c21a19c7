import GUIComponents from '@q/gui';

export { default as GUICardItem } from './GUICardItem';
export { default as GUIFormCardEditorModal } from './GUIFormCardEditorModal';
export { default as FormCardEditor } from './FormCardEditor';
export { default as HoverOperationPannel } from './HoverOperationPannel';
export { default as CollapsibleCategory } from './CollapsibleCategory';
export { default as DivideLayout } from './DivideLayout';
export { default as GUICardDescriptionPanel } from './GUICardDescriptionPanel';

export interface GUICardData {
  id: string;
  title: string;
  form_data: SDKSchema;
}

export interface SchemaItem {
  id: string;
  type: string;
  props: Record<string, any>;
}

export interface VariableConfig {
  type: string;
  description: string;
}

export interface SchemaItemWithVariable {
  id: string;
  type: string;
  props: Record<string, any>;
  variable: Record<string, VariableConfig>;
}

export interface SDKSchema {
  description: string;
  schema: SchemaItemWithVariable[];
}

/**
 * 将 SchemaItem 格式的数据转换成 SDK Schema 格式
 * @param schemaItems SchemaItem数组
 * @param description 模板描述，默认为"自定义模板"
 * @returns 转换后的Schema对象
 */
export function convertSchemaItemsToSDKSchema(
  schemaItems: SchemaItem[],
  description: string = '自定义模板'
): SDKSchema {
  const convertedSchema: SchemaItemWithVariable[] = schemaItems.map((item) => {
    // 如果 hasUserOverrides 为 true，则不获取 variable
    const variable = isComponentHasUserOverrides(item.type, item.props)
      ? null
      : getComponentVariable(item.type, item.props);
    return {
      id: item.id,
      type: item.type,
      props: {
        ...item.props,
      },
      variable,
    };
  });

  return {
    description,
    schema: convertedSchema,
  };
}

/**
 * 将 SDK Schema 格式转换成 SchemaItem 格式
 * @param sdkSchema SDK Schema 格式
 * @returns SchemaItem 格式
 */
export function convertSDKSchemaToSchemaItems(sdkSchema: SDKSchema): SchemaItem[] {
  return sdkSchema.schema.map((item) => {
    return {
      id: item.id,
      type: item.type,
      props: item.props,
    };
  });
}

export function getComponentMetadata(type: string) {
  const component = GUIComponents[type];
  if (!component) {
    console.error(`获取对应组件 ${type} 的元数据失败`);
    return null;
  }
  return component;
}

export function getComponentDescription(type: string, props: Record<string, any>) {
  const component = getComponentMetadata(type);
  if (!component) {
    return '';
  }
  return component.getDescription?.(props) || '';
}

export function getComponentVariable(type: string, props: Record<string, any>) {
  const component = getComponentMetadata(type);
  if (!component) {
    return null;
  }
  return component.getVariable?.(props) || null;
}

export function isComponentHasUserOverrides(type: string, props: Record<string, any>) {
  const component = getComponentMetadata(type);
  if (!component) {
    return false;
  }
  return component?.hasUserOverrides?.(props) || false;
}
