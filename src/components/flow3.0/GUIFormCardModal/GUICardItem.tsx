import React, { useMemo } from 'react';
import classnames from 'classnames';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';

import { convertSDKSchemaToSchemaItems, DivideLayout, type GUICardData } from './index';
import GUIComponent from './GUIComponent';
import styles from './GUICardItem.module.scss';

interface GUICardItemProps {
  data: GUICardData;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

function GUICardItem({ data, onEdit, onDelete }: GUICardItemProps) {
  const schemaItems = useMemo(() => {
    try {
      if (data.form_data && data.form_data.schema && Array.isArray(data.form_data.schema)) {
        return convertSDKSchemaToSchemaItems(data.form_data);
      }
      return [];
    } catch (error) {
      console.warn('转换 schema 失败:', error);
      return [];
    }
  }, [data.form_data]);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(data.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(data.id);
  };

  const title = data.title || '自定义界面';

  return (
    <div className={styles.GUICardItem}>
      <div className={styles.header}>
        <div className={classnames(styles.title, 'truncate')}>{title}</div>
        <div className={styles.operation}>
          <DivideLayout direction="horizontal" spacing="8px" dividerStyle={{ height: '12px' }}>
            <Button
              className={styles.editButton}
              type="link"
              icon={<EditOutlined />}
              onClick={handleEdit}
              size="small"
            >
              编辑
            </Button>
            <Button type="link" icon={<DeleteOutlined />} onClick={handleDelete} size="small" />
          </DivideLayout>
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.contentInner}>
          {schemaItems.length > 0 ? (
            schemaItems.map((item) => (
              <div key={item.id} className={styles.componentPreviewItem}>
                <GUIComponent item={item} isEditing={false} onPropsChange={() => {}} />
              </div>
            ))
          ) : (
            <div className={styles.emptyComponents}>暂无组件内容</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GUICardItem;
