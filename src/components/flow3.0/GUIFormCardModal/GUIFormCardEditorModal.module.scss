.GUIFormCardEditorModal {
  & > div,
  :global(.ant-modal-content) {
    height: 100%;
    overflow: hidden;
  }
  :global(.ant-modal-content) {
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
  }

  /* 全屏模式下的样式 */
  :global(.ant-modal) {
    max-width: none;
  }

  :global(.ant-modal-header) {
    margin-bottom: 0;
  }

  :global(.ant-modal-body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
    & > div {
      height: 100%;
      overflow: auto;
    }
  }

  :global(.ant-typography-edit) {
    color: #657083;
    margin-left: 8px;
  }

  :global(.ant-typography-edit-content) {
    margin-left: 8px !important;
  }

  :global(.ant-typography-edit) {
    height: 16px;
    width: 16px;
    font-size: 16px;
  }

  .modalHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    min-height: 56px;
    padding: 16px 24px;
    box-sizing: border-box;
    border-radius: 8px 8px 0 0;
    margin: -20px -24px 0 -24px; /* 抵消Modal默认padding */
    background-color: #f7f8fa;
    border-bottom: 1px solid #ebf0f5;

    .modalHeaderLeft {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      flex: 1;

      .modalTitleWrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        flex: 1;
      }

      .modalTitle {
        color: #1b2532;
        flex: 1;
        min-width: 0;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }
    }

    .modalHeaderRight {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;

      .headerIcon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.45);
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.04);
          color: #1b2532;
        }

        :global(.anticon) {
          font-size: 16px;
        }
      }
    }
  }

  .modalFooter {
    height: 56px;
    padding: 16px 0;
    box-sizing: border-box;
    border-radius: 0px 0px 8px 8px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
  }

  .modalFooterButton {
    height: 32px;
    padding: 5px 16px;
    box-sizing: border-box;
  }

  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 300px);
    padding-top: 24px;
  }
  .GUICardDescriptionPannelContainer,
  .editorPannelContainer {
    border-radius: 12px;
    border: 1px solid #e1e7ed;
    overflow: hidden;
  }

  .GUICardDescriptionPannelContainer {
    margin-bottom: 16px;
  }

  .editorPannelContainer {
    flex: 1;
    display: flex;
  }
  .componentsPalette {
    width: 280px;
    overflow: hidden;
    padding: 12px 12px;
    background-color: #fff;
    overflow-x: hidden;
    overflow-y: auto;

    .paletteContent {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .componentsList {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-auto-rows: 98px;
      gap: 0px;
      &.input {
        .componentIcon {
          background-color: #f4f2ff;
        }
      }
      &.display {
        .componentIcon {
          background-color: #e6f7ff;
        }
      }
    }

    .componentItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      user-select: none;
      padding: 6px;
      border-radius: 8px;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        .componentLabel {
          color: #006bff;
        }
      }

      .componentIcon {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
      }

      .componentLabel {
        color: #1d2531;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
  .formCardEditor {
    position: relative;
    flex: 1;
    padding: 32px 24px;
    overflow-y: auto;
    background-color: #edf1f5;
    .editorContainer {
      max-width: 600px;
      margin: 0 auto;
    }
    .GUICardDescriptionPannelContainer {
      margin-bottom: 16px;
    }
  }
  .categoryTitle {
    padding: 0 12px !important;
  }
}
