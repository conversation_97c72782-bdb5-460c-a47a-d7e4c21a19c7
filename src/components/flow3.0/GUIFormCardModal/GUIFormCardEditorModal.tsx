import React, { useEffect, useState, useMemo } from 'react';
import { Button, Form, Modal, Tabs, Card, Spin, Typography, message } from 'antd';
import { FullscreenOutlined, CloseOutlined, LeftOutlined } from '@ant-design/icons';
import { nanoid } from 'nanoid';
import GUIComponents from '@q/gui';

import FormCardEditor from './FormCardEditor';
import CollapsibleCategory from './CollapsibleCategory';
import GUICardDescriptionPanel from './GUICardDescriptionPanel';
import styles from './GUIFormCardEditorModal.module.scss';
import {
  convertSchemaItemsToSDKSchema,
  convertSDKSchemaToSchemaItems,
  type SchemaItem,
} from './index';
import classNames from 'classnames';

const { Paragraph } = Typography;

// 定义组件类型
interface GUIComponent {
  type: string;
  category: string;
  name: string;
  icon?: string;
  originalProps: Record<string, any>;
}

interface GUIComponentsType {
  [key: string]: GUIComponent;
}

interface GUIFormCardEditorModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm?: (cardSchema: any, cardDescription: string, cardTitle?: string, hasInputComponent?: boolean) => Promise<void>;
  confirmLoading?: boolean;
  editingCard?: any;
  isLoadingCardDetail?: boolean;
  cardListLength?: number;
}

export default function GUIFormCardEditorModal({
  open,
  onCancel,
  onConfirm,
  confirmLoading,
  editingCard,
  isLoadingCardDetail,
  cardListLength = 0,
}: GUIFormCardEditorModalProps) {
  const [cardSchema, setCardSchema] = useState<SchemaItem[]>([]);
  const [editingComponentId, setEditingComponentId] = useState('');
  const [cardDescription, setCardDescription] = useState('');
  const [cardTitle, setCardTitle] = useState('');
  const [isFullScreen, setIsFullScreen] = useState(false);

  const resetState = () => {
    setCardSchema([]);
    setEditingComponentId('');
    setCardDescription('');
    setCardTitle('');
    setIsFullScreen(false);
  };

  // 当模态框关闭时重置状态
  useEffect(() => {
    if (!open) {
      resetState();
    }
  }, [open]);

  // 当编辑卡片时预填充数据
  useEffect(() => {
    if (editingCard && editingCard.form_data) {
      try {
        const formData = editingCard.form_data;
        console.debug('编辑卡片 form_data:', formData);

        // 设置标题
        if (editingCard.title) {
          setCardTitle(editingCard.title);
        } else {
          setCardTitle('自定义界面');
        }

        // 设置描述
        if (formData.description) {
          setCardDescription(formData.description);
        }

        // 转换 SDK Schema 回编辑器格式
        if (formData.schema && Array.isArray(formData.schema)) {
          console.debug('加载编辑卡片的 schema:', formData.schema);
          const sdkSchema = {
            description: formData.description || '',
            schema: formData.schema,
          };
          const editorSchema = convertSDKSchemaToSchemaItems(sdkSchema);
          console.debug('转换后的编辑器 schema:', editorSchema);
          setCardSchema(editorSchema);
        }
      } catch (error) {
        console.error('解析编辑卡片数据失败:', error);
      }
    } else {
      // 新建卡片时设置默认标题，包含当前卡片数量
      const defaultTitle = `自定义界面${cardListLength + 1}`;
      setCardTitle(defaultTitle);
    }
  }, [editingCard, cardListLength]);

  // 根据 GUIComponents 生成组件分类
  const componentCategories = useMemo(() => {
    const categories = {
      input: [],
      display: [],
    };

    const typedGUIComponents = GUIComponents as GUIComponentsType;
    Object.entries(typedGUIComponents).forEach(([key, component]) => {
      const categoryItem = {
        key,
        name: component.name,
        type: component.type,
        category: component.category,
        icon:
          component.icon ||
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        originalProps: component.originalProps || {},
      };

      if (component.category === 'input') {
        categories.input.push(categoryItem);
      } else if (component.category === 'display') {
        categories.display.push(categoryItem);
      }
    });

    return categories;
  }, []);

  const onAddComponent = (item) => {
    setCardSchema([
      ...cardSchema,
      {
        id: nanoid(),
        type: item.key, // 使用 key 作为组件类型标识
        props: { ...item.originalProps },
      },
    ]);
  };

  const onDelete = (id: string) => {
    setCardSchema(cardSchema.filter((item) => item.id !== id));
  };

  const onOrderChange = (id: string, direction: 'up' | 'down') => {
    if (cardSchema.length === 1) return;
    const index = cardSchema.findIndex((item) => item.id === id);
    const newSchema = [...cardSchema];
    if (direction === 'up') {
      [newSchema[index], newSchema[index - 1]] = [newSchema[index - 1], newSchema[index]];
    } else {
      [newSchema[index], newSchema[index + 1]] = [newSchema[index + 1], newSchema[index]];
    }
    setCardSchema(newSchema);
  };

  const onSpecComponentPropsChange = (id: string, props: Record<string, any>) => {
    setCardSchema(cardSchema.map((item) => (item.id === id ? { ...item, props } : item)));
  };

  const renderComponentsCategory = (categoryItems, categoryName) => (
    <CollapsibleCategory
      title={categoryName === 'input' ? '输入组件' : '展示组件'}
      defaultExpanded={true}
      titleClassName={styles.categoryTitle}
    >
      <div
        className={classNames(styles.componentsList, {
          [styles.input]: categoryName === 'input',
          [styles.display]: categoryName === 'display',
        })}
      >
        {categoryItems.map((item) => (
          <div key={item.key} className={styles.componentItem} onClick={() => onAddComponent(item)}>
            <img src={item.icon} alt={item.name} className={styles.componentIcon} />
            <div className={styles.componentLabel}>{item.name}</div>
          </div>
        ))}
      </div>
    </CollapsibleCategory>
  );

  const handleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleConfirm = async () => {
    // 验证描述是否填写
    if (!cardDescription || cardDescription.trim() === '') {
      message.warning('提示词不能为空');
      return;
    }

    if (onConfirm) {
      const sdkSchema = convertSchemaItemsToSDKSchema(cardSchema, cardDescription);
      
      // 计算是否包含输入组件
      const typedGUIComponents = GUIComponents as GUIComponentsType;
      const hasInputComponent = cardSchema.some((item) => {
        const component = typedGUIComponents[item.type];
        return component && component.category === 'input';
      });
      
      // 传递 schema 数组、描述、标题和是否包含输入组件
      const defaultTitle = editingCard ? '自定义界面' : `自定义界面${cardListLength + 1}`;
      await onConfirm(sdkSchema.schema, cardDescription, cardTitle || defaultTitle, hasInputComponent);
    }
  };

  const defaultTitle = editingCard ? '自定义界面' : `自定义界面${cardListLength + 1}`;
  const displayTitle = cardTitle || defaultTitle;

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <div className={styles.modalHeaderLeft}>
            <div className={styles.modalTitleWrapper}>
              <Paragraph
                editable={{ 
                  onChange: setCardTitle,
                  maxLength: 255
                }}
                ellipsis={{ tooltip: displayTitle }}
                className={styles.modalTitle}
                style={{
                  margin: 0,
                  flex: 1,
                  maxWidth: 'none',
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: '24px',
                  color: '#1b2532',
                }}
              >
                {displayTitle}
              </Paragraph>
            </div>
          </div>
          <div className={styles.modalHeaderRight}>
            <div className={styles.headerIcon} onClick={handleFullScreen}>
              <FullscreenOutlined />
            </div>
            <div className={styles.headerIcon} onClick={onCancel}>
              <CloseOutlined />
            </div>
          </div>
        </div>
      }
      open={open}
      onCancel={onCancel}
      width={isFullScreen ? '100vw' : '70%'}
      height={isFullScreen ? '100vh' : '85%'}
      style={isFullScreen ? { top: 0, maxWidth: 'none', padding: 0 } : {}}
      className={styles.GUIFormCardEditorModal}
      confirmLoading={confirmLoading || isLoadingCardDetail}
      onOk={handleConfirm}
      closable={false}
      centered={true}
      destroyOnClose={true}
      maskClosable={false}
      transitionName=""
      footer={
        <div className={styles.modalFooter}>
          <Button onClick={onCancel} className={styles.modalFooterButton}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleConfirm}
            loading={confirmLoading || isLoadingCardDetail}
            className={styles.modalFooterButton}
          >
            {editingCard ? '保存' : '创建'}
          </Button>
        </div>
      }
    >
      {isLoadingCardDetail ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '400px',
          }}
        >
          <Spin size="large" tip="加载卡片详情中..." />
        </div>
      ) : (
        <div className={styles.container}>
          <div className={styles.GUICardDescriptionPannelContainer}>
            <GUICardDescriptionPanel value={cardDescription} onChange={setCardDescription} />
          </div>
          <div className={styles.editorPannelContainer}>
            <div className={styles.componentsPalette}>
              <div className={styles.paletteContent}>
                {renderComponentsCategory(componentCategories.input, 'input')}
                {renderComponentsCategory(componentCategories.display, 'display')}
              </div>
            </div>

            <div className={styles.formCardEditor}>
              <div className={styles.editorContainer}>
                <FormCardEditor
                  schema={cardSchema}
                  editingComponentId={editingComponentId}
                  onSpecComponentPropsChange={onSpecComponentPropsChange}
                  onDelete={onDelete}
                  onOrderChange={onOrderChange}
                  onEditingChange={setEditingComponentId}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
}
