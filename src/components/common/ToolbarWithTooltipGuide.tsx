import React, { useState } from 'react'
import { Button, Popover } from 'antd'
import FlowEditorTooltipGuide from './FlowEditorTooltipGuide'

/**
 * 带有Tooltip引导的工具栏示例组件
 * 演示如何在实际业务组件中集成tooltip引导
 */
const ToolbarWithTooltipGuide: React.FC = () => {
  const [hoveredElement, setHoveredElement] = useState<string>('')
  const [isShowLeft, setIsShowLeft] = useState(false)
  const [openPopover, setOpenPopover] = useState(false)

  // 模拟添加节点的函数
  const handleAddNode = (nodeType: string) => {
    console.log('添加节点:', nodeType)
    setOpenPopover(false)
  }

  // 添加块内容组件
  const AddBlockContent = () => (
    <div style={{ padding: '10px', minWidth: '200px' }}>
      <div style={{ marginBottom: '10px', fontWeight: 'bold' }}>选择智能体类型</div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Button 
          type="text" 
          onClick={() => handleAddNode('expert_agent')}
          style={{ textAlign: 'left', justifyContent: 'flex-start' }}
        >
          🤖 专家智能体
        </Button>
        <Button 
          type="text" 
          onClick={() => handleAddNode('condition_agent')}
          style={{ textAlign: 'left', justifyContent: 'flex-start' }}
        >
          🔀 条件智能体
        </Button>
        <Button 
          type="text" 
          onClick={() => handleAddNode('loop_agent')}
          style={{ textAlign: 'left', justifyContent: 'flex-start' }}
        >
          🔄 循环智能体
        </Button>
      </div>
    </div>
  )

  return (
    <div style={{ 
      padding: '20px', 
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <h1 style={{ marginBottom: '30px', textAlign: 'center' }}>
        工具栏Tooltip引导集成示例
      </h1>

      {/* 说明区域 */}
      <div style={{ 
        marginBottom: '30px', 
        padding: '20px', 
        backgroundColor: 'white', 
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h3>使用说明：</h3>
        <ul>
          <li>将鼠标悬停在"添加智能体"按钮上，会显示相关引导</li>
          <li>悬停在工具栏区域会显示工具栏引导</li>
          <li>点击"显示左侧菜单"后悬停左侧区域会显示菜单引导</li>
          <li>引导只会在每台机器上显示一次（可通过调试按钮重置）</li>
        </ul>
      </div>

      {/* 模拟工具栏 */}
      <div 
        style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '15px',
          padding: '15px 20px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          marginBottom: '20px'
        }}
        onMouseEnter={() => setHoveredElement('toolbar')}
        onMouseLeave={() => setHoveredElement('')}
      >
        <h4 style={{ margin: 0, color: '#333' }}>工具栏</h4>
        
        {/* 添加智能体按钮 */}
        <Popover
          content={<AddBlockContent />}
          trigger="click"
          open={openPopover}
          onOpenChange={setOpenPopover}
          placement="bottomLeft"
        >
          <Button
            type="primary"
            size="large"
            onMouseEnter={() => setHoveredElement('addAgentButton')}
            onMouseLeave={() => setHoveredElement('')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="white"/>
            </svg>
            添加智能体
          </Button>
        </Popover>

        <Button onClick={() => setIsShowLeft(!isShowLeft)}>
          {isShowLeft ? '隐藏' : '显示'}左侧菜单
        </Button>

        <Button>保存</Button>
        <Button>运行</Button>
      </div>

      {/* 主要内容区域 */}
      <div style={{ display: 'flex', gap: '20px' }}>
        {/* 左侧菜单 */}
        {isShowLeft && (
          <div 
            style={{ 
              width: '250px',
              backgroundColor: 'white',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              padding: '20px'
            }}
            onMouseEnter={() => setHoveredElement('leftMenu')}
            onMouseLeave={() => setHoveredElement('')}
          >
            <h4 style={{ marginBottom: '15px' }}>智能体组件</h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
              <div style={{ 
                padding: '12px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px',
                cursor: 'pointer'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>🤖 专家智能体</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  通过大模型自主使用工具、知识库，完成推理任务
                </div>
              </div>
              <div style={{ 
                padding: '12px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px',
                cursor: 'pointer'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>🔀 条件智能体</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  通过设定条件连接判断下游分支，完成流程分支
                </div>
              </div>
              <div style={{ 
                padding: '12px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px',
                cursor: 'pointer'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>🔄 循环智能体</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  重复执行指定的任务流程
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 主要工作区域 */}
        <div style={{ 
          flex: 1,
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          padding: '40px',
          textAlign: 'center'
        }}>
          <h3>工作流画布区域</h3>
          <p style={{ color: '#666', marginBottom: '30px' }}>
            这里是流程编辑器的主要工作区域
          </p>
          
          {/* 状态显示 */}
          <div style={{ 
            padding: '20px', 
            backgroundColor: '#f8f9fa', 
            borderRadius: '6px',
            textAlign: 'left'
          }}>
            <h4>当前状态：</h4>
            <p>悬停元素: <code>{hoveredElement || '无'}</code></p>
            <p>左侧菜单: <code>{isShowLeft ? '显示' : '隐藏'}</code></p>
            <p>弹窗状态: <code>{openPopover ? '打开' : '关闭'}</code></p>
          </div>
        </div>
      </div>

      {/* 集成Tooltip引导组件 */}
      <FlowEditorTooltipGuide
        isShowLeft={isShowLeft}
        isNewUser={true} // 设为true以便测试
        hoveredElement={hoveredElement}
        enableGuide={true}
      />
    </div>
  )
}

export default ToolbarWithTooltipGuide
