import React from "react"
import styles from "./BubbleTooltip.module.scss"
import type { FixedBubbleTooltipProps } from '../../types/tooltipGuide'

const FixedBubbleTooltip: React.FC<FixedBubbleTooltipProps> = ({
  content,
  direction = "left",
  showDot = true,
  style = {},
  visible = true
}) => {
  const DotIcon = () => (
    <img src="https://s5.ssl.qhres2.com/static/8981edabeb39e098.svg" alt="" />
  )

  if (!visible) {
    return null
  }

  return (
    <div
      className={`${styles.bubbleTooltip} ${styles[direction]}`}
      style={{
        position: 'fixed',
        ...style
      }}
    >
      {/* 气泡内容 */}
      <div className={styles.bubbleContent}>{content}</div>

      {/* 圆点 */}
      {showDot && (
        <div className={styles.bubbleDot}>
          <DotIcon />
        </div>
      )}
    </div>
  )
}

export default FixedBubbleTooltip
