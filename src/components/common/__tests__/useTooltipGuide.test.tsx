import { renderHook, act } from '@testing-library/react'
import { useTooltipGuide } from '../../../hooks/useTooltipGuide'
import type { ImageBubbleConfig } from '../../../types/tooltipGuide'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 测试配置
const testBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "测试提示1",
        style: { left: '50%', top: '20%' },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1,
    bubbles: [
      {
        content: "测试提示2",
        style: { right: '20%', bottom: '30%' },
        direction: "left",
        showDot: true
      }
    ]
  }
]

describe('useTooltipGuide', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  it('应该正确初始化', () => {
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    expect(result.current.visibleTooltips).toEqual({})
    expect(result.current.hasVisibleTooltips()).toBe(false)
    expect(result.current.getShownImageKeys()).toEqual([])
  })

  it('应该能够显示和隐藏tooltip', () => {
    localStorageMock.getItem.mockReturnValue('[]')
    
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    act(() => {
      result.current.showTooltip(0)
    })
    
    expect(result.current.visibleTooltips[0]).toBe(true)
    expect(result.current.hasVisibleTooltips()).toBe(true)
    
    act(() => {
      result.current.hideTooltip(0)
    })
    
    expect(result.current.visibleTooltips[0]).toBe(false)
    expect(result.current.hasVisibleTooltips()).toBe(false)
  })

  it('应该能够批量操作tooltip', () => {
    localStorageMock.getItem.mockReturnValue('[]')
    
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    act(() => {
      result.current.showMultipleTooltips([0, 1])
    })
    
    expect(result.current.visibleTooltips[0]).toBe(true)
    expect(result.current.visibleTooltips[1]).toBe(true)
    
    act(() => {
      result.current.hideMultipleTooltips([0, 1])
    })
    
    expect(result.current.visibleTooltips[0]).toBe(false)
    expect(result.current.visibleTooltips[1]).toBe(false)
  })

  it('应该正确处理localStorage记录', () => {
    localStorageMock.getItem.mockReturnValue('[]')
    
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    act(() => {
      result.current.showTooltip(0)
    })
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'nami_tooltip_guide_records',
      JSON.stringify([{ imageKey: 0, hasShow: true }])
    )
  })

  it('应该跳过已显示过的tooltip', () => {
    localStorageMock.getItem.mockReturnValue(
      JSON.stringify([{ imageKey: 0, hasShow: true }])
    )
    
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    expect(result.current.hasShown(0)).toBe(true)
    
    act(() => {
      result.current.showTooltip(0)
    })
    
    expect(result.current.visibleTooltips[0]).toBeUndefined()
  })

  it('应该能够重置显示记录', () => {
    const { result } = renderHook(() => useTooltipGuide(testBubbleConfig))
    
    act(() => {
      result.current.resetShowRecords()
    })
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('nami_tooltip_guide_records')
  })

  it('应该支持禁用localStorage', () => {
    const { result } = renderHook(() => 
      useTooltipGuide(testBubbleConfig, { enableStorage: false })
    )
    
    act(() => {
      result.current.showTooltip(0)
    })
    
    expect(localStorageMock.setItem).not.toHaveBeenCalled()
    expect(result.current.visibleTooltips[0]).toBe(true)
  })
})
