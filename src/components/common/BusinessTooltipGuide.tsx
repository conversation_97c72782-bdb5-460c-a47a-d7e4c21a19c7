import React, { useEffect } from 'react'
import { useTooltipGuide } from '../../hooks/useTooltipGuide'
import type { ImageBubbleConfig, BusinessTooltipGuideProps } from '../../types/tooltipGuide'

// 业务场景的Tooltip配置
const businessBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0, // 首页引导
    bubbles: [
      {
        content: "欢迎使用纳米智能体！点击这里开始创建您的第一个智能体",
        style: {
          left: '50%',
          top: '20%',
          transform: 'translateX(-50%)'
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1, // 创建智能体面板引导
    bubbles: [
      {
        content: "在这里输入智能体的名称和描述",
        style: {
          right: '20%',
          top: '15%'
        },
        direction: "left",
        showDot: true
      },
      {
        content: "选择合适的AI模型来驱动您的智能体",
        style: {
          right: '20%',
          top: '40%'
        },
        direction: "left",
        showDot: true
      }
    ]
  },
  {
    imageKey: 2, // 工具配置引导
    bubbles: [
      {
        content: "添加工具可以大大增强智能体的能力",
        style: {
          left: '30%',
          bottom: '20%'
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 3, // 知识库引导
    bubbles: [
      {
        content: "连接知识库让智能体更加准确和专业",
        style: {
          right: '25%',
          top: '30%'
        },
        direction: "left",
        showDot: true
      }
    ]
  }
]



/**
 * 业务场景的Tooltip引导组件
 * 根据用户行为和页面状态自动显示相应的引导
 */
const BusinessTooltipGuide: React.FC<BusinessTooltipGuideProps> = ({
  currentPage = 'home',
  isNewUser = false,
  panelStates = {}
}) => {
  const {
    showTooltip,
    hideTooltip,
    hideAllTooltips,
    renderTooltips,
    hasShown
  } = useTooltipGuide(businessBubbleConfig, {
    enableStorage: true,
    autoHideOnUnmount: true,
    storageKey: 'business_tooltip_guide_records'
  })

  // 根据页面状态自动显示引导
  useEffect(() => {
    // 新用户首次进入首页时显示欢迎引导
    if (currentPage === 'home' && isNewUser && !hasShown(0)) {
      const timer = setTimeout(() => {
        showTooltip(0)
      }, 1000) // 延迟1秒显示，让用户适应界面
      
      return () => clearTimeout(timer)
    }
  }, [currentPage, isNewUser, showTooltip, hasShown])

  // 监听面板状态变化
  useEffect(() => {
    // 创建面板打开时显示引导
    if (panelStates.createPanel && !hasShown(1)) {
      const timer = setTimeout(() => {
        showTooltip(1)
      }, 500)
      return () => clearTimeout(timer)
    } else if (!panelStates.createPanel) {
      hideTooltip(1)
    }
  }, [panelStates.createPanel, showTooltip, hideTooltip, hasShown])

  useEffect(() => {
    // 工具面板打开时显示引导
    if (panelStates.toolsPanel && !hasShown(2)) {
      const timer = setTimeout(() => {
        showTooltip(2)
      }, 500)
      return () => clearTimeout(timer)
    } else if (!panelStates.toolsPanel) {
      hideTooltip(2)
    }
  }, [panelStates.toolsPanel, showTooltip, hideTooltip, hasShown])

  useEffect(() => {
    // 知识库面板打开时显示引导
    if (panelStates.knowledgePanel && !hasShown(3)) {
      const timer = setTimeout(() => {
        showTooltip(3)
      }, 500)
      return () => clearTimeout(timer)
    } else if (!panelStates.knowledgePanel) {
      hideTooltip(3)
    }
  }, [panelStates.knowledgePanel, showTooltip, hideTooltip, hasShown])

  // 页面切换时隐藏所有引导
  useEffect(() => {
    return () => {
      hideAllTooltips()
    }
  }, [currentPage, hideAllTooltips])

  return (
    <>
      {/* 渲染所有激活的Tooltip */}
      {renderTooltips()}
    </>
  )
}

export default BusinessTooltipGuide
