import React, { useEffect } from 'react'
import { useTooltipGuide } from '../../hooks/useTooltipGuide'
import type { ImageBubbleConfig } from '../../types/tooltipGuide'

// 流程编辑器的Tooltip配置
const flowEditorBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0, // 添加智能体按钮引导
    bubbles: [
      {
        content: "点击这里添加专家智能体到您的工作流中",
        style: {
          left: '15%',
          top: '12%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      },
      {
        content: "专家智能体可以自主使用工具和知识库完成复杂任务",
        style: {
          left: '15%',
          top: '22%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1, // 工具栏引导
    bubbles: [
      {
        content: "这里是工具栏，包含各种编辑功能",
        style: {
          right: '20%',
          top: '8%',
          zIndex: 10001
        },
        direction: "left",
        showDot: true
      }
    ]
  },
  {
    imageKey: 2, // 左侧菜单引导
    bubbles: [
      {
        content: "从这里选择不同类型的智能体组件",
        style: {
          left: '25%',
          top: '30%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  }
]

interface FlowEditorTooltipGuideProps {
  // 是否显示左侧菜单
  isShowLeft?: boolean
  // 是否是新用户
  isNewUser?: boolean
  // 当前悬停的元素
  hoveredElement?: string
  // 是否启用引导
  enableGuide?: boolean
}

/**
 * 流程编辑器Tooltip引导组件
 * 专门为流程编辑器页面设计的引导系统
 */
const FlowEditorTooltipGuide: React.FC<FlowEditorTooltipGuideProps> = ({
  isShowLeft = false,
  isNewUser = false,
  hoveredElement = '',
  enableGuide = true
}) => {
  const {
    showTooltip,
    hideTooltip,
    hideAllTooltips,
    renderTooltips,
    hasShown,
    resetShowRecords
  } = useTooltipGuide(flowEditorBubbleConfig, {
    enableStorage: true,
    storageKey: 'flow_editor_tooltip_guide',
    autoHideOnUnmount: true
  })

  // 根据悬停元素显示对应引导
  useEffect(() => {
    if (!enableGuide) return

    switch (hoveredElement) {
      case 'addAgentButton':
        if (!hasShown(0)) {
          showTooltip(0)
        }
        break
      case 'toolbar':
        if (!hasShown(1)) {
          showTooltip(1)
        }
        break
      case 'leftMenu':
        if (isShowLeft && !hasShown(2)) {
          showTooltip(2)
        }
        break
      default:
        // 当没有悬停任何元素时，隐藏所有引导
        hideAllTooltips()
        break
    }
  }, [hoveredElement, enableGuide, isShowLeft, showTooltip, hideAllTooltips, hasShown])

  // 新用户首次进入时的欢迎引导
  useEffect(() => {
    if (isNewUser && enableGuide && !hasShown(0)) {
      const timer = setTimeout(() => {
        showTooltip(0)
        // 3秒后自动隐藏
        setTimeout(() => {
          hideTooltip(0)
        }, 3000)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [isNewUser, enableGuide, showTooltip, hideTooltip, hasShown])

  // 开发模式下的调试控制
  const renderDebugControls = () => {
    if (process.env.NODE_ENV !== 'development') return null

    return (
      <div style={{
        position: 'fixed',
        bottom: '80px',
        right: '20px',
        zIndex: 10002,
        background: 'white',
        padding: '10px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        fontSize: '12px',
        minWidth: '200px'
      }}>
        <div style={{ marginBottom: '8px' }}>
          <strong>流程编辑器引导调试</strong>
        </div>
        <div style={{ marginBottom: '8px', fontSize: '11px', color: '#666' }}>
          悬停元素: {hoveredElement || '无'}
        </div>
        <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap', marginBottom: '8px' }}>
          <button 
            onClick={() => showTooltip(0)} 
            style={{ fontSize: '10px', padding: '2px 4px' }}
          >
            显示添加按钮引导
          </button>
          <button 
            onClick={() => showTooltip(1)} 
            style={{ fontSize: '10px', padding: '2px 4px' }}
          >
            显示工具栏引导
          </button>
          <button 
            onClick={() => showTooltip(2)} 
            style={{ fontSize: '10px', padding: '2px 4px' }}
          >
            显示左菜单引导
          </button>
        </div>
        <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
          <button 
            onClick={hideAllTooltips} 
            style={{ fontSize: '10px', padding: '2px 4px' }}
          >
            隐藏所有
          </button>
          <button 
            onClick={resetShowRecords} 
            style={{ fontSize: '10px', padding: '2px 4px', color: 'red' }}
          >
            重置记录
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* 渲染所有Tooltip */}
      {renderTooltips()}
      
      {/* 开发模式调试控制 */}
      {renderDebugControls()}
    </>
  )
}

export default FlowEditorTooltipGuide
