import React, { useState, useEffect } from 'react'
import { useTooltipGuide } from '../../hooks/useTooltipGuide'
import type { ImageBubbleConfig } from '../../types/tooltipGuide'

// 完整的业务场景配置
const integratedBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0, // 欢迎引导
    bubbles: [
      {
        content: "欢迎使用纳米智能体平台！让我们开始您的AI之旅",
        style: {
          left: '50%',
          top: '10%',
          transform: 'translateX(-50%)',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1, // 创建按钮引导
    bubbles: [
      {
        content: "点击这里创建您的第一个智能体",
        style: {
          left: '20%',
          top: '15%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 2, // 配置面板引导
    bubbles: [
      {
        content: "在这里配置智能体的基本信息",
        style: {
          right: '25%',
          top: '20%',
          zIndex: 10001
        },
        direction: "left",
        showDot: true
      },
      {
        content: "选择合适的AI模型",
        style: {
          right: '25%',
          top: '35%',
          zIndex: 10001
        },
        direction: "left",
        showDot: true
      },
      {
        content: "添加系统提示词",
        style: {
          right: '25%',
          top: '50%',
          zIndex: 10001
        },
        direction: "left",
        showDot: true
      }
    ]
  },
  {
    imageKey: 3, // 工具配置引导
    bubbles: [
      {
        content: "为智能体添加强大的工具能力",
        style: {
          left: '30%',
          bottom: '25%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 4, // 完成引导
    bubbles: [
      {
        content: "恭喜！您已经成功创建了第一个智能体",
        style: {
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  }
]

interface IntegratedTooltipGuideProps {
  // 用户状态
  isNewUser?: boolean
  // 当前步骤
  currentStep?: number
  // 面板状态
  panels?: {
    welcome?: boolean
    create?: boolean
    config?: boolean
    tools?: boolean
    complete?: boolean
  }
  // 回调函数
  onStepComplete?: (step: number) => void
  onGuideComplete?: () => void
}

/**
 * 集成的新手引导组件
 * 提供完整的用户引导流程
 */
const IntegratedTooltipGuide: React.FC<IntegratedTooltipGuideProps> = ({
  isNewUser = false,
  currentStep = 0,
  panels = {},
  onStepComplete,
  onGuideComplete
}) => {
  const [guideActive, setGuideActive] = useState(false)
  const [currentGuideStep, setCurrentGuideStep] = useState(0)

  const {
    showTooltip,
    hideTooltip,
    hideAllTooltips,
    renderTooltips,
    hasShown,
    visibleTooltips,
    hasVisibleTooltips
  } = useTooltipGuide(integratedBubbleConfig, {
    enableStorage: true,
    storageKey: 'integrated_tooltip_guide',
    autoHideOnUnmount: true
  })

  // 开始引导流程
  const startGuide = () => {
    if (!isNewUser) return
    
    setGuideActive(true)
    setCurrentGuideStep(0)
    showTooltip(0) // 显示欢迎引导
  }

  // 进入下一步
  const nextStep = () => {
    const nextStepIndex = currentGuideStep + 1
    
    // 隐藏当前步骤
    hideTooltip(currentGuideStep)
    
    if (nextStepIndex < integratedBubbleConfig.length) {
      setCurrentGuideStep(nextStepIndex)
      
      // 延迟显示下一步，让用户有时间适应
      setTimeout(() => {
        showTooltip(nextStepIndex)
      }, 500)
      
      onStepComplete?.(currentGuideStep)
    } else {
      // 引导完成
      completeGuide()
    }
  }

  // 完成引导
  const completeGuide = () => {
    setGuideActive(false)
    hideAllTooltips()
    onGuideComplete?.()
  }

  // 跳过引导
  const skipGuide = () => {
    setGuideActive(false)
    hideAllTooltips()
  }

  // 监听面板状态变化，自动推进引导
  useEffect(() => {
    if (!guideActive) return

    // 根据面板状态自动推进引导
    if (panels.create && currentGuideStep === 0) {
      nextStep() // 从欢迎进入创建引导
    } else if (panels.config && currentGuideStep === 1) {
      nextStep() // 从创建进入配置引导
    } else if (panels.tools && currentGuideStep === 2) {
      nextStep() // 从配置进入工具引导
    } else if (panels.complete && currentGuideStep === 3) {
      nextStep() // 从工具进入完成引导
    }
  }, [panels, guideActive, currentGuideStep])

  // 新用户自动开始引导
  useEffect(() => {
    if (isNewUser && !hasShown(0)) {
      const timer = setTimeout(() => {
        startGuide()
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [isNewUser, hasShown])

  // 渲染引导控制按钮（开发调试用）
  const renderGuideControls = () => {
    if (process.env.NODE_ENV !== 'development') return null

    return (
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 10002,
        background: 'white',
        padding: '10px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        fontSize: '12px'
      }}>
        <div style={{ marginBottom: '8px' }}>
          <strong>引导控制 (开发模式)</strong>
        </div>
        <div style={{ marginBottom: '8px' }}>
          当前步骤: {currentGuideStep} | 激活: {guideActive ? '是' : '否'}
        </div>
        <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
          <button onClick={startGuide} style={{ fontSize: '11px', padding: '2px 6px' }}>
            开始引导
          </button>
          <button onClick={nextStep} style={{ fontSize: '11px', padding: '2px 6px' }}>
            下一步
          </button>
          <button onClick={skipGuide} style={{ fontSize: '11px', padding: '2px 6px' }}>
            跳过引导
          </button>
          <button onClick={completeGuide} style={{ fontSize: '11px', padding: '2px 6px' }}>
            完成引导
          </button>
        </div>
        <div style={{ marginTop: '8px', fontSize: '10px', color: '#666' }}>
          可见Tooltip: {Object.keys(visibleTooltips).filter(key => visibleTooltips[parseInt(key)]).join(', ') || '无'}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* 渲染所有Tooltip */}
      {renderTooltips()}
      
      {/* 开发模式控制面板 */}
      {renderGuideControls()}
      
      {/* 引导遮罩层（可选） */}
      {guideActive && hasVisibleTooltips() && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          zIndex: 10000,
          pointerEvents: 'none'
        }} />
      )}
    </>
  )
}

export default IntegratedTooltipGuide
