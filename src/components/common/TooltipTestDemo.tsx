import React, { useState } from 'react'
import { Button } from 'antd'
import { useTooltipGuide } from '../../hooks/useTooltipGuide'
import type { ImageBubbleConfig } from '../../types/tooltipGuide'

// 测试用的Tooltip配置
const testBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0, // 添加智能体按钮引导
    bubbles: [
      {
        content: "点击这里可以创建一个新的智能体",
        style: {
          left: '20%',
          top: '15%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      },
      {
        content: "智能体可以帮助您自动化处理各种任务",
        style: {
          left: '20%',
          top: '25%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1, // 其他按钮引导
    bubbles: [
      {
        content: "这是另一个功能按钮的说明",
        style: {
          right: '20%',
          top: '35%',
          zIndex: 10001
        },
        direction: "left",
        showDot: true
      }
    ]
  }
]

/**
 * Tooltip测试演示组件
 * 演示如何在鼠标悬停时显示/隐藏引导
 */
const TooltipTestDemo: React.FC = () => {
  const [hoverStates, setHoverStates] = useState<Record<string, boolean>>({})

  const {
    showTooltip,
    hideTooltip,
    renderTooltips,
    resetShowRecords,
    hasShown,
    visibleTooltips
  } = useTooltipGuide(testBubbleConfig, {
    enableStorage: false, // 测试时禁用localStorage，方便重复测试
    autoHideOnUnmount: true,
    storageKey: 'tooltip_test_demo'
  })

  // 处理鼠标悬停进入
  const handleMouseEnter = (buttonId: string, imageKey: number) => {
    setHoverStates(prev => ({ ...prev, [buttonId]: true }))
    showTooltip(imageKey)
  }

  // 处理鼠标悬停离开
  const handleMouseLeave = (buttonId: string, imageKey: number) => {
    setHoverStates(prev => ({ ...prev, [buttonId]: false }))
    hideTooltip(imageKey)
  }

  return (
    <div style={{ 
      padding: '40px', 
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <h1 style={{ marginBottom: '30px', textAlign: 'center' }}>
        Tooltip引导测试演示
      </h1>
      
      {/* 测试说明 */}
      <div style={{ 
        marginBottom: '30px', 
        padding: '20px', 
        backgroundColor: 'white', 
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h3>测试说明：</h3>
        <ul>
          <li>将鼠标悬停在"添加智能体"按钮上，会显示imageKey为0的所有引导</li>
          <li>鼠标移开时，引导会自动隐藏</li>
          <li>每个按钮都可以独立控制对应的引导显示</li>
          <li>测试模式下禁用了localStorage，可以重复测试</li>
        </ul>
      </div>

      {/* 状态显示 */}
      <div style={{ 
        marginBottom: '30px', 
        padding: '15px', 
        backgroundColor: '#e6f7ff', 
        borderRadius: '8px',
        border: '1px solid #91d5ff'
      }}>
        <h4>当前状态：</h4>
        <p>悬停状态: {JSON.stringify(hoverStates)}</p>
        <p>可见Tooltip: {Object.entries(visibleTooltips).filter(([_, visible]) => visible).map(([key]) => key).join(', ') || '无'}</p>
        <p>已显示记录: {[0, 1].filter(key => hasShown(key)).join(', ') || '无'}</p>
      </div>

      {/* 测试按钮区域 */}
      <div style={{ 
        display: 'flex', 
        gap: '20px', 
        flexWrap: 'wrap',
        marginBottom: '30px'
      }}>
        {/* 添加智能体按钮 */}
        <Button
          type="primary"
          size="large"
          onMouseEnter={() => handleMouseEnter('addAgent', 0)}
          onMouseLeave={() => handleMouseLeave('addAgent', 0)}
          style={{
            backgroundColor: hoverStates.addAgent ? '#1890ff' : '#40a9ff',
            borderColor: hoverStates.addAgent ? '#1890ff' : '#40a9ff',
            transform: hoverStates.addAgent ? 'scale(1.05)' : 'scale(1)',
            transition: 'all 0.2s ease'
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style={{ marginRight: '8px' }}>
            <path fillRule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="white"/>
          </svg>
          添加智能体
        </Button>

        {/* 其他功能按钮 */}
        <Button
          size="large"
          onMouseEnter={() => handleMouseEnter('otherFeature', 1)}
          onMouseLeave={() => handleMouseLeave('otherFeature', 1)}
          style={{
            backgroundColor: hoverStates.otherFeature ? '#f0f0f0' : 'white',
            transform: hoverStates.otherFeature ? 'scale(1.05)' : 'scale(1)',
            transition: 'all 0.2s ease'
          }}
        >
          其他功能
        </Button>

        {/* 配置按钮 */}
        <Button
          size="large"
          type="dashed"
          style={{
            cursor: 'default'
          }}
        >
          普通按钮（无引导）
        </Button>
      </div>

      {/* 控制按钮 */}
      <div style={{ 
        marginBottom: '30px',
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h4>手动控制：</h4>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <Button onClick={() => showTooltip(0)}>
            手动显示引导0
          </Button>
          <Button onClick={() => hideTooltip(0)}>
            手动隐藏引导0
          </Button>
          <Button onClick={() => showTooltip(1)}>
            手动显示引导1
          </Button>
          <Button onClick={() => hideTooltip(1)}>
            手动隐藏引导1
          </Button>
          <Button onClick={resetShowRecords} danger>
            重置显示记录
          </Button>
        </div>
      </div>

      {/* 代码示例 */}
      <div style={{ 
        padding: '20px',
        backgroundColor: '#f6f8fa',
        borderRadius: '8px',
        border: '1px solid #d0d7de'
      }}>
        <h4>实现代码示例：</h4>
        <pre style={{ 
          backgroundColor: '#f1f3f4', 
          padding: '15px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '12px'
        }}>
{`// 鼠标悬停显示引导
const handleMouseEnter = (buttonId: string, imageKey: number) => {
  showTooltip(imageKey)
}

// 鼠标离开隐藏引导
const handleMouseLeave = (buttonId: string, imageKey: number) => {
  hideTooltip(imageKey)
}

// 在按钮上使用
<Button
  onMouseEnter={() => handleMouseEnter('addAgent', 0)}
  onMouseLeave={() => handleMouseLeave('addAgent', 0)}
>
  添加智能体
</Button>`}
        </pre>
      </div>

      {/* 渲染所有Tooltip */}
      {renderTooltips()}
    </div>
  )
}

export default TooltipTestDemo
