import React from 'react'
import { useTooltipGuide, ImageBubbleConfig } from '../../hooks/useTooltipGuide'

// 示例配置
const exampleBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "自由编排一个专家智能体",
        style: {
          left: '62.8%',
          bottom: '27%'
        },
        direction: "right",
        showDot: true
      },
      {
        content: "选择一个已有专家来解决问题",
        style: {
          left: '62.8%',
          bottom: '10%'
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1,
    bubbles: [
      {
        content: "告诉专家怎么做",
        style: {
          right: '28.8%',
          top: '19.2%'
        },
        direction: "left",
        showDot: true
      },
      {
        content: "可以添加各种工具提升专家能力",
        style: {
          right: '28.8%',
          top: '56.6%'
        },
        direction: "left",
        showDot: true
      }
    ]
  },
  {
    imageKey: 2,
    bubbles: [
      {
        content: "输入内容，自动批量执行",
        style: {
          right: '28.8%',
          top: '19.2%'
        },
        direction: "left",
        showDot: true
      }
    ]
  }
]

const TooltipGuideExample: React.FC = () => {
  const {
    showTooltip,
    hideTooltip,
    hideAllTooltips,
    resetShowRecords,
    renderTooltips,
    hasShown,
    getShownImageKeys,
    hasVisibleTooltips,
    showMultipleTooltips,
    hideMultipleTooltips,
    visibleTooltips
  } = useTooltipGuide(exampleBubbleConfig, {
    enableStorage: true,
    autoHideOnUnmount: true
  })

  return (
    <div style={{ padding: '20px' }}>
      <h2>Tooltip引导示例</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={() => showTooltip(0)}
          style={{ marginRight: '10px' }}
        >
          显示引导0 {hasShown(0) ? '(已显示过)' : ''}
        </button>
        
        <button 
          onClick={() => showTooltip(1)}
          style={{ marginRight: '10px' }}
        >
          显示引导1 {hasShown(1) ? '(已显示过)' : ''}
        </button>
        
        <button 
          onClick={() => showTooltip(2)}
          style={{ marginRight: '10px' }}
        >
          显示引导2 {hasShown(2) ? '(已显示过)' : ''}
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={() => hideTooltip(0)}
          style={{ marginRight: '10px' }}
        >
          隐藏引导0
        </button>
        
        <button 
          onClick={() => hideTooltip(1)}
          style={{ marginRight: '10px' }}
        >
          隐藏引导1
        </button>
        
        <button 
          onClick={() => hideTooltip(2)}
          style={{ marginRight: '10px' }}
        >
          隐藏引导2
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={hideAllTooltips}
          style={{ marginRight: '10px' }}
        >
          隐藏所有引导
        </button>

        <button
          onClick={resetShowRecords}
          style={{ marginRight: '10px' }}
        >
          重置显示记录
        </button>

        <button
          onClick={() => showMultipleTooltips([0, 1, 2])}
          style={{ marginRight: '10px' }}
        >
          显示所有引导
        </button>

        <button
          onClick={() => hideMultipleTooltips([0, 1, 2])}
          style={{ marginRight: '10px' }}
        >
          隐藏所有引导(批量)
        </button>
      </div>

      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <h4>状态信息：</h4>
        <p>当前显示的引导: {Object.entries(visibleTooltips).filter(([_, visible]) => visible).map(([key]) => key).join(', ') || '无'}</p>
        <p>已显示过的引导: {getShownImageKeys().join(', ') || '无'}</p>
        <p>是否有引导正在显示: {hasVisibleTooltips() ? '是' : '否'}</p>
      </div>

      <div>
        <p>说明：</p>
        <ul>
          <li>每个引导只会在每台机器上显示一次</li>
          <li>显示过的引导会在localStorage中标记</li>
          <li>可以通过"重置显示记录"来清除标记，重新显示引导</li>
          <li>引导使用fixed定位，会固定在页面指定位置</li>
        </ul>
      </div>

      {/* 渲染所有Tooltip */}
      {renderTooltips()}
    </div>
  )
}

export default TooltipGuideExample
