# useTooltipGuide 新手引导Tooltip自定义Hooks

## 概述

`useTooltipGuide` 是一个用于管理新手引导Tooltip的自定义React Hooks。它提供了完整的Tooltip生命周期管理，包括显示、隐藏、localStorage持久化等功能。

## 特性

- ✅ **固定定位**: 使用`position: fixed`进行页面级定位
- ✅ **localStorage持久化**: 每个引导只在每台机器上显示一次
- ✅ **灵活配置**: 支持多种配置选项
- ✅ **批量操作**: 支持批量显示/隐藏多个Tooltip
- ✅ **状态查询**: 提供丰富的状态查询方法
- ✅ **自动清理**: 组件卸载时自动清理

## 基础用法

### 1. 定义配置

```typescript
import { ImageBubbleConfig } from '../hooks/useTooltipGuide'

const bubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "这是第一个引导提示",
        style: {
          left: '50%',
          top: '20%',
          transform: 'translateX(-50%)'
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1,
    bubbles: [
      {
        content: "这是第二个引导提示",
        style: {
          right: '20%',
          bottom: '30%'
        },
        direction: "left",
        showDot: true
      }
    ]
  }
]
```

### 2. 使用Hooks

```typescript
import { useTooltipGuide } from '../hooks/useTooltipGuide'

const MyComponent = () => {
  const {
    showTooltip,
    hideTooltip,
    renderTooltips
  } = useTooltipGuide(bubbleConfig)

  const handleOpenPanel = () => {
    // 打开面板时显示引导
    showTooltip(0)
  }

  const handleClosePanel = () => {
    // 关闭面板时隐藏引导
    hideTooltip(0)
  }

  return (
    <div>
      <button onClick={handleOpenPanel}>打开面板</button>
      <button onClick={handleClosePanel}>关闭面板</button>
      
      {/* 渲染Tooltip */}
      {renderTooltips()}
    </div>
  )
}
```

## API 参考

### useTooltipGuide(bubbleConfig, options?)

#### 参数

- `bubbleConfig: ImageBubbleConfig[]` - Tooltip配置数组
- `options?: TooltipGuideOptions` - 可选配置

#### TooltipGuideOptions

```typescript
interface TooltipGuideOptions {
  enableStorage?: boolean      // 是否启用localStorage记录（默认true）
  storageKey?: string         // 自定义localStorage key
  autoHideOnUnmount?: boolean // 组件卸载时自动隐藏（默认true）
}
```

#### 返回值

```typescript
{
  // 基础方法
  showTooltip: (imageKey: number) => void
  hideTooltip: (imageKey: number) => void
  hideAllTooltips: () => void
  resetShowRecords: () => void
  renderTooltips: () => React.ReactNode[]
  
  // 状态查询
  visibleTooltips: Record<number, boolean>
  hasShown: (imageKey: number) => boolean
  getShownImageKeys: () => number[]
  hasVisibleTooltips: () => boolean
  
  // 批量操作
  showMultipleTooltips: (imageKeys: number[]) => void
  hideMultipleTooltips: (imageKeys: number[]) => void
  
  // 配置信息
  options: TooltipGuideOptions
}
```

## 高级用法

### 业务场景集成

```typescript
const BusinessComponent = () => {
  const [panelOpen, setPanelOpen] = useState(false)
  
  const {
    showTooltip,
    hideTooltip,
    renderTooltips,
    hasShown
  } = useTooltipGuide(bubbleConfig, {
    storageKey: 'my_business_tooltips'
  })

  useEffect(() => {
    if (panelOpen && !hasShown(1)) {
      // 面板打开且未显示过时，延迟显示引导
      const timer = setTimeout(() => {
        showTooltip(1)
      }, 500)
      return () => clearTimeout(timer)
    } else if (!panelOpen) {
      // 面板关闭时隐藏引导
      hideTooltip(1)
    }
  }, [panelOpen, showTooltip, hideTooltip, hasShown])

  return (
    <div>
      {/* 业务组件 */}
      {renderTooltips()}
    </div>
  )
}
```

### 批量管理

```typescript
const BatchExample = () => {
  const {
    showMultipleTooltips,
    hideMultipleTooltips,
    getShownImageKeys,
    renderTooltips
  } = useTooltipGuide(bubbleConfig)

  const showWelcomeFlow = () => {
    // 显示欢迎流程的所有引导
    showMultipleTooltips([0, 1, 2])
  }

  const hideAllGuides = () => {
    // 隐藏所有引导
    hideMultipleTooltips([0, 1, 2, 3])
  }

  return (
    <div>
      <button onClick={showWelcomeFlow}>开始引导</button>
      <button onClick={hideAllGuides}>结束引导</button>
      <p>已显示过的引导: {getShownImageKeys().join(', ')}</p>
      {renderTooltips()}
    </div>
  )
}
```

## 注意事项

1. **imageKey唯一性**: 确保每个`imageKey`在配置中是唯一的
2. **样式定位**: 使用`position: fixed`，样式中的定位是相对于视口的
3. **localStorage限制**: 在无痕模式或禁用localStorage的环境中，记录功能会失效
4. **性能考虑**: 大量Tooltip同时显示可能影响性能，建议分批显示
5. **响应式设计**: 固定定位在不同屏幕尺寸下可能需要调整样式

## 示例文件

- `src/components/common/TooltipGuideExample.tsx` - 基础使用示例
- `src/components/common/BusinessTooltipGuide.tsx` - 业务场景示例
