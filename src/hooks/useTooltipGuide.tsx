import React, { useState, useEffect, useCallback } from 'react'
import FixedBubbleTooltip from '../components/common/FixedBubbleTooltip'
import type {
  BubbleData,
  ImageBubbleConfig,
  TooltipShowRecord,
  TooltipGuideOptions,
  TooltipGuideReturn
} from '../types/tooltipGuide'

// 重新导出类型以保持向后兼容
export type {
  B<PERSON>bleData,
  ImageBubbleConfig,
  TooltipShowRecord,
  TooltipGuideOptions,
  TooltipGuideReturn
}

// localStorage key
const TOOLTIP_STORAGE_KEY = 'nami_tooltip_guide_records'

/**
 * 新手引导Tooltip自定义hooks
 * @param bubbleConfig Tooltip配置数组
 * @param options 配置选项
 * @returns
 */
export const useTooltipGuide = (
  bubbleConfig: ImageBubbleConfig[],
  options: TooltipGuideOptions = {}
): TooltipGuideReturn => {
  const {
    enableStorage = true,
    storageKey = TOOLTIP_STORAGE_KEY,
    autoHideOnUnmount = true
  } = options
  // 存储每个imageKey对应的显示状态
  const [visibleTooltips, setVisibleTooltips] = useState<Record<number, boolean>>({})
  
  // 从localStorage获取已显示记录
  const getShowRecords = useCallback((): TooltipShowRecord[] => {
    if (!enableStorage) return []

    try {
      const records = localStorage.getItem(storageKey)
      return records ? JSON.parse(records) : []
    } catch (error) {
      console.error('获取Tooltip显示记录失败:', error)
      return []
    }
  }, [enableStorage, storageKey])

  // 保存显示记录到localStorage
  const saveShowRecords = useCallback((records: TooltipShowRecord[]) => {
    if (!enableStorage) return

    try {
      localStorage.setItem(storageKey, JSON.stringify(records))
    } catch (error) {
      console.error('保存Tooltip显示记录失败:', error)
    }
  }, [enableStorage, storageKey])

  // 检查某个imageKey是否已经显示过
  const hasShown = useCallback((imageKey: number): boolean => {
    const records = getShowRecords()
    const record = records.find(r => r.imageKey === imageKey)
    return record?.hasShow || false
  }, [getShowRecords])

  // 标记某个imageKey已显示
  const markAsShown = useCallback((imageKey: number) => {
    const records = getShowRecords()
    const existingIndex = records.findIndex(r => r.imageKey === imageKey)
    
    if (existingIndex >= 0) {
      records[existingIndex].hasShow = true
    } else {
      records.push({ imageKey, hasShow: true })
    }
    
    saveShowRecords(records)
  }, [getShowRecords, saveShowRecords])

  // 显示指定imageKey的所有Tooltip
  const showTooltip = useCallback((imageKey: number) => {
    // 检查是否已经显示过
    if (hasShown(imageKey)) {
      console.log(`Tooltip imageKey ${imageKey} 已经显示过，跳过显示`)
      return
    }

    // 显示Tooltip
    setVisibleTooltips(prev => ({
      ...prev,
      [imageKey]: true
    }))

    // 标记为已显示
    markAsShown(imageKey)
  }, [hasShown, markAsShown])

  // 隐藏指定imageKey的所有Tooltip
  const hideTooltip = useCallback((imageKey: number) => {
    setVisibleTooltips(prev => ({
      ...prev,
      [imageKey]: false
    }))
  }, [])

  // 隐藏所有Tooltip
  const hideAllTooltips = useCallback(() => {
    setVisibleTooltips({})
  }, [])

  // 重置所有显示记录（用于测试或重置功能）
  const resetShowRecords = useCallback(() => {
    if (!enableStorage) return

    try {
      localStorage.removeItem(storageKey)
      setVisibleTooltips({})
    } catch (error) {
      console.error('重置Tooltip显示记录失败:', error)
    }
  }, [enableStorage, storageKey])

  // 组件卸载时自动隐藏所有tooltip
  useEffect(() => {
    return () => {
      if (autoHideOnUnmount) {
        setVisibleTooltips({})
      }
    }
  }, [autoHideOnUnmount])

  // 渲染所有Tooltip组件
  const renderTooltips = useCallback(() => {
    return bubbleConfig.map(config => {
      const isVisible = visibleTooltips[config.imageKey] || false
      
      if (!isVisible) {
        return null
      }

      return config.bubbles.map((bubble, index) => (
        <FixedBubbleTooltip
          key={`${config.imageKey}-${index}`}
          content={bubble.content}
          direction={bubble.direction}
          showDot={bubble.showDot}
          style={bubble.style}
          visible={true}
        />
      ))
    }).filter(Boolean)
  }, [bubbleConfig, visibleTooltips])

  // 获取所有已显示过的imageKey列表
  const getShownImageKeys = useCallback((): number[] => {
    const records = getShowRecords()
    return records.filter(r => r.hasShow).map(r => r.imageKey)
  }, [getShowRecords])

  // 检查是否有任何tooltip正在显示
  const hasVisibleTooltips = useCallback((): boolean => {
    return Object.values(visibleTooltips).some(visible => visible)
  }, [visibleTooltips])

  // 批量显示多个tooltip
  const showMultipleTooltips = useCallback((imageKeys: number[]) => {
    imageKeys.forEach(imageKey => {
      showTooltip(imageKey)
    })
  }, [showTooltip])

  // 批量隐藏多个tooltip
  const hideMultipleTooltips = useCallback((imageKeys: number[]) => {
    imageKeys.forEach(imageKey => {
      hideTooltip(imageKey)
    })
  }, [hideTooltip])

  return {
    // 基础方法
    showTooltip,
    hideTooltip,
    hideAllTooltips,
    resetShowRecords,
    renderTooltips,

    // 状态查询
    visibleTooltips,
    hasShown,
    getShownImageKeys,
    hasVisibleTooltips,

    // 批量操作
    showMultipleTooltips,
    hideMultipleTooltips,

    // 配置信息
    options: {
      enableStorage,
      storageKey,
      autoHideOnUnmount
    }
  }
}
