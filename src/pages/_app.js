/*
 * @Author: zx
 * @Date: 2024-05-29 21:04:30
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-04-21 10:37:16
 * @FilePath: \prompt-web\src\pages\_app.js
 */
import "@/styles/globals.scss";
import "@/styles/flowEditor.scss";
import "@/styles/knowledge.scss";
import "@/styles/deployDetail.scss";
import "@/styles/flow/flow.scss";
import "@/styles/agent.scss";
import '../../public/font/font.css';
import '@q/flowgram.ai.free-layout-editor/index.css';
import { ConfigProvider } from "antd";
import "dayjs/locale/zh-cn";
import zhCN from "antd/lib/locale/zh_CN";
import { RecoilRoot } from "recoil";
import CommonPage from "@/components/common/CommonPage";
import { useRouter } from "next/router";
import { useEffect } from "react";
import * as constants from "@/constants/appConstants";
import moment from 'moment';
import 'moment/locale/zh-cn';
import { initializeEasyRender } from '../utils/easyRenderInit';

// atoms key开发环境warning问题
import { RecoilEnv } from "recoil";
RecoilEnv.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED = false;

moment.locale('zh-cn');

// 初始化全局对象
initializeEasyRender();

// 新创建的 `pages/_app.js` 文件中必须有此默认的导出（export）函数
export default function MyApp({ Component, pageProps }) {
  const router = useRouter();
  const { pathname, query } = router;
  const isBackground = pathname.indexOf("background") > -1;

  const isFullScreen = () => {
    // 1. 检查外部平台来源
    const externalPlatforms = [
      { source: "yunpanBoard", storageKey: constants.prompt_isYunPanBoard },
      { source: "llmopsBoard", storageKey: constants.prompt_isLlmopsBoard },
    ];

    for (const platform of externalPlatforms) {
      if (
        query.source === platform.source ||
        (typeof window !== "undefined" &&
          window.localStorage.getItem(platform.storageKey) === "true")
      ) {
        return true;
      }
    }

    // 2. 检查特定全屏页面路径
    const fullScreenPaths = [
      "operationDetailV3",
      "agentPublish",
      "agentDetail",
      "agentResult",
      "flowDetail",
      "flowInteractionDetail",
      "flowDemo",
      "flowInteractionDemo",
      "/channelTemplList",
      "agentWxAuth",
      "apiDetail",
      "ideDetail",
      "tiggerDetail",
      "apiPublish",
      "agentSDK",
      "agentOpen",
      "deepseek",
      "mobile",
      "transferLogin",
    ];

    for (const path of fullScreenPaths) {
      if (pathname.indexOf(path) > -1) {
        return true;
      }
    }

    return false;
  };
  // 处理外部平台缓存标识
  const handleUpdatePlatform = (router) => {
    // 外部平台
    const otherPlatform = {
      tuituiBoard: constants.prompt_isTuituiBoard,
      zhiyuBoard: constants.prompt_isZhiYuBoard,
      yunpanBoard: constants.prompt_isYunPanBoard,
      companyBrain: constants.prompt_isCompanyBrainBoard,
      llmopsBoard: constants.prompt_isLlmopsBoard,
      qpaasBoard: constants.prompt_isQpaasBoard,
      zhike: constants.prompt_isZhiKeBoard,
      agent: "agent",
    };
    // 公共标识之前接入的平台
    const transformMap = {
      tuituiBoard: "tuituiBoard",
      zhiyuBoard: "zhiyuBoard",
      yunpanBoard: "yunpanBoard",
      companyBrain: "companyBrain",
      qpaasBoard: "qpaasBoard",
      llmopsBoard: "llmopsBoard",
    };
    // 初次进入,设置标识
    if (JSON.stringify(router.query) != "{}" && typeof window !== "undefined") {
      // 更新sessionStorage sourcePlatform
      let source = router.query.source || "";
      // console.log("source===>", source);
      if (otherPlatform[source]) {
        let value = otherPlatform[source];
        if (transformMap[source]) {
          value = transformMap[source];
        }
        window.sessionStorage.setItem("sourcePlatform", value);
      }
    }
    if (typeof window !== "undefined") {
      // 通过sessionStorage sourcePlatform 更新localStorage
      let sourcePlatform = window.sessionStorage.getItem("sourcePlatform");
      // console.log("sourcePlatform===>1", sourcePlatform);
      // 更换窗口,或刷新进行判断
      if (!sourcePlatform) {
        Object.keys(otherPlatform).forEach((item) => {
          window.localStorage.removeItem(otherPlatform[item]);
        });
        // 变更之前的平台
        let platFormValue =
          window.localStorage.getItem(constants.prompt_platform) || "";
        // console.log(
        //   "platFormValue ===>2",
        //   platFormValue,
        //   window.location.href.indexOf("source=") > -1
        // );
        const notAgentPlat = platFormValue !== "agent" && platFormValue !== ""; // 不是agent自己的平台
        if (
          notAgentPlat ||
          (window.location.href.indexOf("source=") > -1 && !notAgentPlat)
        ) {
          window.localStorage.setItem(constants.prompt_userName, "");
          window.localStorage.setItem(constants.prompt_authorization, "");
          window.localStorage.setItem(constants.prompt_userId, "");
          window.localStorage.setItem(constants.prompt_teamId, "");
          window.localStorage.setItem(constants.prompt_platform, "");
          window.localStorage.setItem(constants.prompt_project_list, "");
        }
      } else {
        let value_new = sourcePlatform;
        if (otherPlatform[value_new]) {
          value_new = otherPlatform[value_new];
        }
        // 变更之前的平台
        let platFormValue = window.localStorage.getItem(
          constants.prompt_platform
        );
        console.log(
          "platFormValue===>",
          platFormValue,
          "sourcePlatform===>",
          sourcePlatform
        );
        if (
          !platFormValue ||
          (platFormValue && sourcePlatform !== platFormValue)
        ) {
          window.localStorage.setItem(constants.prompt_userName, "");
          window.localStorage.setItem(constants.prompt_authorization, "");
          window.localStorage.setItem(constants.prompt_userId, "");
          window.localStorage.setItem(constants.prompt_teamId, "");
          window.localStorage.setItem(constants.prompt_project_list, "");
          Object.keys(otherPlatform).forEach((item) => {
            window.localStorage.removeItem(otherPlatform[item]);
          });
        } else {
          // 更新标识
          window.localStorage.setItem(value_new, "true"); // 外部平台标识
        }
        window.localStorage.setItem(constants.prompt_platform, sourcePlatform); // 当前所属平台
      }
    }
  };
  useEffect(() => {
    // 刷新页面,判断是否是外部平台
    handleUpdatePlatform(router);
  }, [router.query]);

  const useComponent = () => {
    if (pathname === "/") return true;
    if (pathname.indexOf("transferLogin") > -1) return true;
    if (pathname.indexOf("flowApp") > -1) return true;
    if (pathname.indexOf("flowNewApp") > -1) return true;
    if (pathname.indexOf("flowBaoApp") > -1) return true;
    // 添加测试页面支持
    if (pathname === "/tooltip-test") return true;
    if (pathname === "/toolbar-tooltip-test") return true;
    return false;
  };

  const useCommonPage = () => {
    if (pathname.indexOf("agentOpen") > -1) return true;
    if (pathname.indexOf("mobile") > -1) return true;
    if (pathname.indexOf("deepseek") > -1) return true;
    if (JSON.stringify(query) != "{}") return true;
    if (isBackground) return true;
    return false;
  };

  return (
    <>
      <ConfigProvider locale={zhCN}>
        <RecoilRoot>
          {useComponent() ? (
            <Component {...pageProps} />
          ) : useCommonPage() ? (
            <CommonPage
              isBackground={isBackground}
              isFullScreen={isFullScreen()}
              children={<Component {...pageProps} />}
            />
          ) : (
            ""
          )}
        </RecoilRoot>
      </ConfigProvider>
    </>
  );
}
