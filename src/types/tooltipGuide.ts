/**
 * Tooltip引导相关的类型定义
 */

// 气泡数据接口
export interface BubbleData {
  content: string
  style: React.CSSProperties
  direction: 'left' | 'right'
  showDot?: boolean
}

// 图片气泡配置接口
export interface ImageBubbleConfig {
  imageKey: number
  bubbles: BubbleData[]
}

// Tooltip显示记录接口
export interface TooltipShowRecord {
  imageKey: number
  hasShow: boolean
}

// Tooltip引导配置选项
export interface TooltipGuideOptions {
  // 是否启用localStorage记录（默认true）
  enableStorage?: boolean
  // 自定义localStorage key
  storageKey?: string
  // 是否在组件卸载时自动隐藏所有tooltip（默认true）
  autoHideOnUnmount?: boolean
}

// Tooltip引导Hooks返回值接口
export interface TooltipGuideReturn {
  // 基础方法
  showTooltip: (imageKey: number) => void
  hideTooltip: (imageKey: number) => void
  hideAllTooltips: () => void
  resetShowRecords: () => void
  renderTooltips: () => React.ReactNode[]
  
  // 状态查询
  visibleTooltips: Record<number, boolean>
  hasShown: (imageKey: number) => boolean
  getShownImageKeys: () => number[]
  hasVisibleTooltips: () => boolean
  
  // 批量操作
  showMultipleTooltips: (imageKeys: number[]) => void
  hideMultipleTooltips: (imageKeys: number[]) => void
  
  // 配置信息
  options: Required<TooltipGuideOptions>
}

// 固定气泡Tooltip组件属性
export interface FixedBubbleTooltipProps {
  content: string
  direction?: 'left' | 'right'
  showDot?: boolean
  style?: React.CSSProperties
  visible?: boolean
}

// 业务Tooltip引导组件属性
export interface BusinessTooltipGuideProps {
  // 当前页面或面板状态
  currentPage?: 'home' | 'create' | 'tools' | 'knowledge'
  // 是否是新用户
  isNewUser?: boolean
  // 面板打开状态
  panelStates?: {
    createPanel?: boolean
    toolsPanel?: boolean
    knowledgePanel?: boolean
  }
}

// 常量定义
export const TOOLTIP_STORAGE_KEYS = {
  DEFAULT: 'nami_tooltip_guide_records',
  BUSINESS: 'business_tooltip_guide_records',
  TUTORIAL: 'tutorial_tooltip_guide_records'
} as const

// Tooltip方向枚举
export enum TooltipDirection {
  LEFT = 'left',
  RIGHT = 'right'
}

// 页面类型枚举
export enum PageType {
  HOME = 'home',
  CREATE = 'create',
  TOOLS = 'tools',
  KNOWLEDGE = 'knowledge'
}
