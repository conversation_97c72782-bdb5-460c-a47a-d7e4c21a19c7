.login{
  text-align: center;
}
.homeBg {
  background: url(https://p1.ssl.qhimg.com/t110b9a9301e861d69b8232060d.png) #000;
  background-repeat: no-repeat;
  background-position: 0 0px;
  background-size: cover;
  overflow: auto;
  min-height: 100vh;
}
.homeBody {
  padding-top: 160px;
  width: 62.5%;
  margin: 0px auto;
  max-width: 1200px;
  text-align: left;

  .homeBodyContent {
    // position: absolute;
    // top: calc(calc(100% - 424px) / 2);
    // left: 20%;
    // width: 504px;

    .bodyTitle {
      // color: var(---, #1B2532);
      // font-family: "360shouhuType-Bold";
      // font-size: 60px;
      // font-style: normal;
      // font-weight: 700;
      height: 74px; /* 173.333% */
    }

    .bodySubTitle {
      color: #fff;
      /* 常规/24px */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      // opacity: 0.6;
      // line-height: 36px; /* 150% */
      width: 998px;
      display: inline-block;
      margin-top: 16px;
      max-width: 90%;
      opacity: 0.6;
      line-height: 28px;
    }

    .bodyBtn {
      margin-top: 40px;
      position: relative;
      z-index: 0;
      cursor: pointer;
    }
    .contentWrapper{
      background: url(https://p4.ssl.qhimg.com/t110b9a9301dfece9e3bc879e8b.png) no-repeat 107% 0;
      background-size: 38%;
      img{
        margin-top: -18.5%;
        width: 126%;
        margin-left: -22%;
      }
    }

  }
}
.header{
  height: 80px;
  width: 100%;
  position: fixed;
  z-index: 1;
  background: #000;
  .headerInner{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    width: 62.5%;
    margin: 13px auto;
  }
  .title{
  }
}
.myAgent{
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .userContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    font-weight: normal;
    font-size: 16px;
  }
}
.login{
  // margin-right: 48px;
  color: #fff;
}
.login .loginBtn{
  line-height: 55px;
  border-radius: 12px;
  background: linear-gradient(92deg, #6358FF 5.84%, #3354FF 98.31%);
  padding: 10px 24px;
  color: #F9FAFB;

  text-align: center;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}
.noLogin{
  display: inline-block;
  margin-top: 17px;
}

.filterWrapper{
  padding: 0 24px 2px;
  display: flex;
  justify-content: space-between;
}
.sortType{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  color: #9BA7BA;
  font-size: 14px;
  height: 32px;
}
.activeSortTypeItem{
  background: linear-gradient(90deg, #6E7CF8 3.33%, #3B73F7 100%);
  border-radius: 30px;
  color: #FFFFFF;
}
.sortTypeItem{
  padding: 5px 16px;
  display: inline-block;
  vertical-align: middle;
  height: 32px;
  cursor: pointer;
}
.modelType{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
}
.tagList{
  padding: 16px 24px 6px;
  display: flex;
}
.tagItems{
  width: calc(100% - 164px);
  height: 32px;
  overflow-x: hidden;
  margin: 0 16px;
  position: relative;
}
.tagInnerItems{
  width: 1825px;
  height: 32px;
  position: absolute;
  left: 0px;
}
.tagItem{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  /* box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04); */
  color: #626F84;
  border-radius: 20px;
  font-size: 12px;
  padding: 6px 16px;
  margin-right: 10px;
  height: 32px;
  line-height: 20px;
  cursor: pointer;
  float: left;
}
.tagActiveItem{
  color: #F2F7FF;
  background: linear-gradient(90deg, #6F7DF9 2.85%, #3D74F8 98.96%);
}
.tagBtn{
  display: inline-block;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  width: 32px;
  height: 32px;
  text-align: center;
  cursor: pointer;
  line-height: 31px;
  color: #818284;
  font-size: 12px;
}

.promptList{
  height: calc(100vh - 164px);
  padding: 10px 24px 24px;
  overflow-y: scroll;
}
.waterFallItem{
  position: relative;
  background: #B5D4FF;
  border-radius: 8px;
  cursor: pointer;
}
.waterFallItem img{
  border-radius: 8px;
  width: 100%;
  display: block;
}
.promptInfo{
  background: rgba(43, 54, 88, 0.8);
  border-radius: 0 0 8px 8px;
  position: absolute;
  width: 100%;
  bottom: 0px;
  color: #fff;
  padding: 12px;
  font-size: 14px;
  display: none;
}
.activePromptInfo{
  display: block;
}
.promptTitle{
  color: #1B2532;
  font-size: 16px;
  padding: 12px 13px;
}
.promptDesc{
  color: #1B2532;
  font-size: 13px;
  padding: 0px 13px 12px;
}
.promptResult{
  color: #1B2532;
  font-size: 12px;
  padding: 0 13px 15px;
}
.promptInfo img{
  width: 20px;
  vertical-align: bottom;
  margin-right: 4px;
  display: inline-block;
}
img.starIcon{
  width: 13px;    
  position: relative;
  top: -4px;
  border-radius: 0px;
}
img.starActiveIcon{
  width: 14px;    
}
.starActiveNum{
  color: #006BFF;
}
.userInfoWrapper{
  background: #fff;
  border-radius: 8px;
  padding: 14px;
  color: #626F84;
  font-size: 14px;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  right: -55px;
  top: -10px;
  z-index: 1;

  .userNameBg,.userInfoInner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;

    img {
      margin-right: 12px;
      width: 20px;
      height: 20px;
    }
  }

  .userInfoInner {
    margin-top: 4px;
    cursor: pointer;
    color: #1B2532;
    img {
      margin-right: 14px;
      width: 17px;
      height: 17px;
    }
  }
}
.userBox{
  padding: 0px;
  display: flex;
  align-items: center;
  a{
    color: #FFF;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-right: 32px;
  }
}
.userWrapper{
  padding: 12px;
}
.userName{
  margin-bottom: 7px;
}
.doc{
  padding: 5px 0;
}
.doc a{
  margin-left: 10px;
}
.userName img{
  margin-right: 8px;
  vertical-align: middle;    
  margin-top: -2px;
}
.logout{
  padding: 5px 0;
  cursor: pointer;
}
.logout:hover{
  opacity: 0.7;
  border-radius: 4px;
}
.logoutBtn{
  margin-left: 10px;
}
.showUserInfoWrapper{
  display: block;
}
.userInfoTitle{
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.userInfoDesc{
  font-size: 12px;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.userInfo{
  display: flex;
  justify-content: space-between;
  line-height: 20px;
  font-size: 12px;
}
.loadMore{
  text-align: center;
  font-size: 12px;
  color: rgba(82, 78, 78, 0.664);
  margin: 10px 0;
  cursor: pointer;
}

/* openDetail */
.openDetail{
  margin-top: 18px;
}
.openDetailContent{
  display: flex;
}
.openDetailLeftContent{
  width: 580px;
  height: 556px;
  border: 1px solid #BCCAD6;
  margin-right: 16px;
  border-radius: 8px;
  text-align: center;
  position: relative;
}
.openTextDetailLeftContent{
  width: 580px;
  height: 556px;
  margin-right: 16px;
  border-radius: 8px;
  text-align: center;
  position: relative;
}
div.promptWrapper{
  height: 240px;
}
div.result{
  height: 296px;
}
.openDetailLeftContent img{
  display: inline-block;
  vertical-align: middle;
  border-radius: 8px;
}
.vline{
  width: 1px;
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.leftImgIcon{
  position: absolute;
  left: 10px;
  top: 261px;
  background: #fff;
  padding: 10px;
  border-radius: 20px;
  cursor: pointer;
}
.rightImgIcon{
  right: 10px;
  position: absolute;
  top: 261px;
  background: #fff;
  padding: 10px;
  border-radius: 20px;
  cursor: pointer;
}
.pageIcon{
  position: absolute;
  bottom: 0px;
  left: 280px;
  color: #aaa;
  font-size: 12px;
}
img.hideImg{
  display: none;
}

.openDetailRightContent{
  width: 356px;
  font-size: 12px;
  position: relative;
}
.detailTitleWrapper{
  /* border-bottom: 1px solid #BCCAD6; */
  margin-bottom: 16px;
}
.detailTitle{
  color: #1B2532;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}
.detailDesc{
  color: #1B2532;
  font-weight: 400;
  padding-bottom: 16px;
  border-bottom: 1px solid #BCCAD6;
}
.detailPrompt{
  color: #1B2532;
  font-weight: 400;
  border-bottom: 1px solid #BCCAD6;
  padding-bottom: 16px;
}
.detailPromptTitle{
  color: #626F84;
  margin-bottom: 3px;
}
.inputWrapper{
  padding: 10px 16px;
}
.paramItem{
  width: 50%;
  display: inline-block;
  margin-top: 16px;
}
.paramItemTitle{
  color: #626F84;margin-bottom: 3px;
}
.copyBtn{
  position: absolute;
  right: 0;
  bottom: -50px;
  margin: 0;
}

.openDetailFoot{
  width: 580px;
  color: #626F84;
  display: flex;
  font-size: 12px;
  justify-content: space-between;
  margin-top: 20px;
}
.openDetailFootLeft{
  width: 300px;
  display: flex;
  justify-content: flex-start;
  margin-top: 6px;
}
.openDetailTime{
  margin-right: 10px;
}
.modalBg{
  background: url(../images/common/bg.png);
  background-size: cover;
  width: 1000px!important;
  height: 690px;
  border-radius: 10px;
}

// footer
.homeFooter{
  width: 100%;
  text-align: center;
  font-size: 12px;
  margin-bottom: 10px;
  color: #999;
  line-height: 18px;
}

.mobileHomeBg{
  background: #000;
}