# Tooltip引导功能测试指南

## 🚀 快速开始

我已经为您创建了完整的Tooltip引导系统，现在您可以通过以下方式测试效果：

## 📋 测试页面

### 1. 基础功能测试页面
**访问地址**: `/tooltip-test`

**功能特点**:
- 鼠标悬停"添加智能体"按钮显示引导
- 鼠标移开自动隐藏引导
- 实时状态显示
- 手动控制按钮
- 代码示例展示

**测试步骤**:
1. 启动项目: `npm run dev`
2. 访问: `http://localhost:3000/tooltip-test`
3. 将鼠标悬停在蓝色的"添加智能体"按钮上
4. 观察页面左上角出现的引导气泡
5. 移开鼠标，引导自动消失

### 2. 工具栏集成测试页面
**访问地址**: `/toolbar-tooltip-test`

**功能特点**:
- 模拟真实的工具栏环境
- 多个区域的悬停引导
- 左侧菜单联动
- 开发调试控制面板

**测试步骤**:
1. 访问: `http://localhost:3000/toolbar-tooltip-test`
2. 悬停在"添加智能体"按钮上查看引导
3. 悬停在工具栏其他区域查看不同引导
4. 点击"显示左侧菜单"，然后悬停左侧区域
5. 使用右下角的调试控制面板进行更多测试

## 🔧 集成到现有业务组件

### 在现有的Toolbar组件中集成

如果您想在现有的 `src/components/flowEditor3.0/Toolbar.tsx` 中集成tooltip引导，可以按以下步骤操作：

1. **导入必要的组件和hooks**:
```typescript
import { useState } from 'react'
import { useTooltipGuide } from '../../hooks/useTooltipGuide'
import type { ImageBubbleConfig } from '../../types/tooltipGuide'
```

2. **定义配置**:
```typescript
const toolbarBubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "点击这里添加专家智能体",
        style: {
          left: '15%',
          top: '12%',
          zIndex: 10001
        },
        direction: "right",
        showDot: true
      }
    ]
  }
]
```

3. **在组件中使用**:
```typescript
const [hoveredElement, setHoveredElement] = useState('')

const { showTooltip, hideTooltip, renderTooltips } = useTooltipGuide(
  toolbarBubbleConfig,
  { enableStorage: true }
)

// 在按钮上添加事件
<Button
  onMouseEnter={() => {
    setHoveredElement('addAgent')
    showTooltip(0)
  }}
  onMouseLeave={() => {
    setHoveredElement('')
    hideTooltip(0)
  }}
>
  添加智能体
</Button>

// 在组件末尾渲染tooltip
{renderTooltips()}
```

## 🎯 具体的业务场景测试

### 场景1: 鼠标悬停显示引导

```typescript
// 在任何按钮或元素上添加
onMouseEnter={() => showTooltip(0)}  // 显示imageKey为0的所有引导
onMouseLeave={() => hideTooltip(0)}  // 隐藏imageKey为0的所有引导
```

### 场景2: 面板打开时显示引导

```typescript
useEffect(() => {
  if (panelOpen && !hasShown(1)) {
    showTooltip(1)  // 面板打开时显示引导
  } else if (!panelOpen) {
    hideTooltip(1)  // 面板关闭时隐藏引导
  }
}, [panelOpen, showTooltip, hideTooltip, hasShown])
```

### 场景3: 新用户首次访问

```typescript
useEffect(() => {
  if (isNewUser && !hasShown(0)) {
    const timer = setTimeout(() => {
      showTooltip(0)
    }, 1000)  // 延迟1秒显示
    return () => clearTimeout(timer)
  }
}, [isNewUser, showTooltip, hasShown])
```

## 🛠️ 调试工具

### 1. 开发模式控制面板
在开发环境中，测试页面会显示调试控制面板，包含：
- 当前状态显示
- 手动显示/隐藏按钮
- 重置localStorage记录

### 2. 浏览器控制台
```javascript
// 查看localStorage中的记录
JSON.parse(localStorage.getItem('nami_tooltip_guide_records'))

// 清除记录（重新测试）
localStorage.removeItem('nami_tooltip_guide_records')
```

### 3. React DevTools
可以查看hooks的状态变化和组件渲染情况

## 📝 配置说明

### Tooltip定位样式
```typescript
style: {
  left: '50%',           // 距离左边50%
  top: '20%',            // 距离顶部20%
  transform: 'translateX(-50%)',  // 水平居中
  zIndex: 10001          // 确保在最上层
}
```

### 常用配置选项
```typescript
const { showTooltip, hideTooltip, renderTooltips } = useTooltipGuide(
  bubbleConfig,
  {
    enableStorage: true,           // 启用localStorage记录
    storageKey: 'my_tooltips',     // 自定义存储key
    autoHideOnUnmount: true        // 组件卸载时自动隐藏
  }
)
```

## ⚠️ 注意事项

1. **z-index层级**: 确保tooltip的z-index足够高（建议10001以上）
2. **定位精确性**: 使用百分比定位，在不同屏幕尺寸下效果更好
3. **性能考虑**: 避免同时显示过多tooltip
4. **用户体验**: 适当的延迟显示和自动隐藏时间

## 🔄 下一步

1. 测试基础功能是否正常
2. 根据实际业务需求调整配置
3. 集成到具体的业务组件中
4. 根据用户反馈优化体验

如果您在测试过程中遇到任何问题，请告诉我具体的错误信息或期望的效果，我会帮您解决！
