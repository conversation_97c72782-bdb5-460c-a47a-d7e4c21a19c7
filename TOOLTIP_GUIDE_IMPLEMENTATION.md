# 新手引导Tooltip实现方案

## 📋 项目概述

本项目实现了一个完整的新手引导Tooltip系统，基于React Hooks构建，支持固定定位、localStorage持久化、批量管理等功能。

## 🏗️ 架构设计

### 核心文件结构
```
src/
├── components/common/
│   ├── BubbleTooltip.tsx              # 原始气泡组件
│   ├── FixedBubbleTooltip.tsx         # 固定定位气泡组件
│   ├── TooltipGuideExample.tsx        # 基础使用示例
│   ├── BusinessTooltipGuide.tsx       # 业务场景示例
│   ├── IntegratedTooltipGuide.tsx     # 完整集成示例
│   └── __tests__/
│       └── useTooltipGuide.test.tsx   # 单元测试
├── hooks/
│   ├── useTooltipGuide.tsx            # 核心Hooks
│   └── useTooltipGuide.md             # 使用文档
└── types/
    └── tooltipGuide.ts                # 类型定义
```

### 设计原则

1. **单一职责**: 每个组件和Hooks只负责特定功能
2. **可复用性**: 通过配置驱动，支持多种业务场景
3. **类型安全**: 完整的TypeScript类型定义
4. **性能优化**: 使用React.memo和useCallback优化渲染
5. **用户体验**: 支持localStorage持久化，避免重复显示

## 🔧 核心功能

### 1. FixedBubbleTooltip 组件
- 基于原有BubbleTooltip样式
- 使用`position: fixed`进行页面级定位
- 支持左右方向箭头
- 可控制显示/隐藏状态

### 2. useTooltipGuide Hooks
- **状态管理**: 管理多个Tooltip的显示状态
- **localStorage持久化**: 记录已显示的引导，避免重复
- **批量操作**: 支持批量显示/隐藏多个Tooltip
- **灵活配置**: 支持自定义存储key、禁用存储等选项

### 3. 类型系统
- 完整的TypeScript类型定义
- 统一的接口规范
- 类型安全的API设计

## 🚀 使用方法

### 基础使用

```typescript
import { useTooltipGuide, ImageBubbleConfig } from '../hooks/useTooltipGuide'

const config: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "这是引导提示",
        style: { left: '50%', top: '20%' },
        direction: "right",
        showDot: true
      }
    ]
  }
]

const MyComponent = () => {
  const { showTooltip, hideTooltip, renderTooltips } = useTooltipGuide(config)
  
  return (
    <div>
      <button onClick={() => showTooltip(0)}>显示引导</button>
      {renderTooltips()}
    </div>
  )
}
```

### 业务场景集成

```typescript
// 监听业务状态变化
useEffect(() => {
  if (panelOpen && !hasShown(1)) {
    showTooltip(1) // 面板打开时显示引导
  } else if (!panelOpen) {
    hideTooltip(1) // 面板关闭时隐藏引导
  }
}, [panelOpen, showTooltip, hideTooltip, hasShown])
```

## 📊 配置说明

### ImageBubbleConfig 配置
```typescript
interface ImageBubbleConfig {
  imageKey: number        // 唯一标识符
  bubbles: BubbleData[]   // 气泡数据数组
}

interface BubbleData {
  content: string                    // 显示内容
  style: React.CSSProperties        // 定位样式
  direction: 'left' | 'right'       // 箭头方向
  showDot?: boolean                  // 是否显示圆点
}
```

### TooltipGuideOptions 选项
```typescript
interface TooltipGuideOptions {
  enableStorage?: boolean      // 启用localStorage (默认true)
  storageKey?: string         // 自定义存储key
  autoHideOnUnmount?: boolean // 组件卸载时自动隐藏 (默认true)
}
```

## 🎯 最佳实践

### 1. 定位样式建议
```css
/* 推荐使用百分比定位 */
style: {
  left: '50%',
  top: '20%',
  transform: 'translateX(-50%)', /* 居中对齐 */
  zIndex: 10001 /* 确保在最上层 */
}
```

### 2. 业务逻辑集成
```typescript
// 延迟显示，让用户适应界面
const timer = setTimeout(() => {
  showTooltip(imageKey)
}, 500)

// 清理定时器
return () => clearTimeout(timer)
```

### 3. 错误处理
```typescript
// Hooks内部已处理localStorage异常
// 业务代码中可以安全调用所有方法
try {
  showTooltip(0)
} catch (error) {
  console.error('显示引导失败:', error)
}
```

## 🧪 测试

### 运行测试
```bash
npm test -- useTooltipGuide.test.tsx
```

### 测试覆盖
- ✅ 基础显示/隐藏功能
- ✅ localStorage持久化
- ✅ 批量操作
- ✅ 配置选项
- ✅ 错误处理

## 🔍 调试工具

### 开发模式控制面板
在开发环境中，`IntegratedTooltipGuide`组件会显示调试控制面板，包含：
- 当前引导状态
- 手动控制按钮
- 可见Tooltip列表

### localStorage查看
```javascript
// 浏览器控制台查看存储记录
JSON.parse(localStorage.getItem('nami_tooltip_guide_records'))
```

## 🚨 注意事项

1. **imageKey唯一性**: 确保每个imageKey在配置中唯一
2. **z-index层级**: 建议使用10001以上的z-index值
3. **响应式适配**: 固定定位在不同屏幕尺寸下需要调整
4. **性能考虑**: 避免同时显示过多Tooltip
5. **无痕模式**: localStorage在无痕模式下可能失效

## 🔄 扩展建议

### 1. 动画增强
- 添加进入/退出动画
- 支持自定义动画效果

### 2. 交互增强
- 支持点击关闭
- 添加"下一步"按钮
- 支持键盘导航

### 3. 样式扩展
- 支持自定义主题
- 响应式样式适配
- 暗色模式支持

### 4. 功能扩展
- 支持视频/图片内容
- 添加进度指示器
- 支持分支引导流程

## 📝 更新日志

- **v1.0.0**: 初始版本，基础功能实现
- 支持固定定位Tooltip
- localStorage持久化
- 批量操作功能
- 完整类型定义
- 单元测试覆盖
